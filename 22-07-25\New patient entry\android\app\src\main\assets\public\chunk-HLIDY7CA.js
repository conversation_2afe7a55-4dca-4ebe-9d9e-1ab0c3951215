import{a as u}from"./chunk-B7F22SOJ.js";import"./chunk-XFXTD7QR.js";import{c as r,i as c,m as a,n as l,o as d,p as o}from"./chunk-EHNA26RN.js";import{g as s}from"./chunk-2R6CW7ES.js";var f=":host(.tab-hidden){display:none !important}",T=(()=>{let e=class{constructor(t){c(this,t),this.loaded=!1,this.active=!1}componentWillLoad(){return s(this,null,function*(){this.active&&(yield this.setActive())})}setActive(){return s(this,null,function*(){yield this.prepareLazyLoaded(),this.active=!0})}changeActive(t){t&&this.prepareLazyLoaded()}prepareLazyLoaded(){if(!this.loaded&&this.component!=null){this.loaded=!0;try{return u(this.delegate,this.el,this.component,["ion-page"])}catch(t){r("[ion-tab] - Exception in prepareLazyLoaded:",t)}}return Promise.resolve(void 0)}render(){let{tab:t,active:i,component:n}=this;return a(l,{key:"dbad8fe9f1566277d14647626308eaf1601ab01f",role:"tabpanel","aria-hidden":i?null:"true","aria-labelledby":`tab-button-${t}`,class:{"ion-page":n===void 0,"tab-hidden":!i}},a("slot",{key:"3be64f4e7161f6769aaf8e4dcb5293fcaa09af45"}))}get el(){return d(this)}static get watchers(){return{active:["changeActive"]}}};return e.style=f,e})(),g=":host{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:100%;height:100%;contain:layout size style;z-index:0}.tabs-inner{position:relative;-ms-flex:1;flex:1;contain:layout size style}",y=class{constructor(e){c(this,e),this.ionNavWillLoad=o(this,"ionNavWillLoad",7),this.ionTabsWillChange=o(this,"ionTabsWillChange",3),this.ionTabsDidChange=o(this,"ionTabsDidChange",3),this.transitioning=!1,this.useRouter=!1,this.onTabClicked=t=>{let{href:i,tab:n}=t.detail;if(this.useRouter&&i!==void 0){let b=document.querySelector("ion-router");b&&b.push(i)}else this.select(n)}}componentWillLoad(){return s(this,null,function*(){if(this.useRouter||(this.useRouter=(!!this.el.querySelector("ion-router-outlet")||!!document.querySelector("ion-router"))&&!this.el.closest("[no-router]")),!this.useRouter){let e=this.tabs;e.length>0&&(yield this.select(e[0]))}this.ionNavWillLoad.emit()})}componentWillRender(){let e=this.el.querySelector("ion-tab-bar");if(e){let t=this.selectedTab?this.selectedTab.tab:void 0;e.selectedTab=t}}select(e){return s(this,null,function*(){let t=h(this.tabs,e);return this.shouldSwitch(t)?(yield this.setActive(t),yield this.notifyRouter(),this.tabSwitch(),!0):!1})}getTab(e){return s(this,null,function*(){return h(this.tabs,e)})}getSelected(){return Promise.resolve(this.selectedTab?this.selectedTab.tab:void 0)}setRouteId(e){return s(this,null,function*(){let t=h(this.tabs,e);return this.shouldSwitch(t)?(yield this.setActive(t),{changed:!0,element:this.selectedTab,markVisible:()=>this.tabSwitch()}):{changed:!1,element:this.selectedTab}})}getRouteId(){return s(this,null,function*(){var e;let t=(e=this.selectedTab)===null||e===void 0?void 0:e.tab;return t!==void 0?{id:t,element:this.selectedTab}:void 0})}setActive(e){return this.transitioning?Promise.reject("transitioning already happening"):(this.transitioning=!0,this.leavingTab=this.selectedTab,this.selectedTab=e,this.ionTabsWillChange.emit({tab:e.tab}),e.active=!0,Promise.resolve())}tabSwitch(){let e=this.selectedTab,t=this.leavingTab;this.leavingTab=void 0,this.transitioning=!1,e&&t!==e&&(t&&(t.active=!1),this.ionTabsDidChange.emit({tab:e.tab}))}notifyRouter(){if(this.useRouter){let e=document.querySelector("ion-router");if(e)return e.navChanged("forward")}return Promise.resolve(!1)}shouldSwitch(e){let t=this.selectedTab;return e!==void 0&&e!==t&&!this.transitioning}get tabs(){return Array.from(this.el.querySelectorAll("ion-tab"))}render(){return a(l,{key:"73ecd3294ca6c78ce6d8b6a7e5b6ccb11d84ada4",onIonTabButtonClick:this.onTabClicked},a("slot",{key:"09661b26f07a3069a58e76ea4dceb9a6acbf365d",name:"top"}),a("div",{key:"db50d59fad8f9b11873b695fc548f3cfe4aceb6a",class:"tabs-inner"},a("slot",{key:"02694dde2d8381f48fc06dd9e79798c4bd540ccd"})),a("slot",{key:"92c4661a5f3fa1c08c964fab7c422c1a2a03d3d8",name:"bottom"}))}get el(){return d(this)}},h=(e,t)=>{let i=typeof t=="string"?e.find(n=>n.tab===t):t;return i||r(`[ion-tabs] - Tab with id: "${i}" does not exist`),i};y.style=g;export{T as ion_tab,y as ion_tabs};
