# 🚀 Android Live Reload Development Setup

## 📱 **Real-Time Development with Android Studio**

This guide shows you how to run your Ionic app on Android with **real-time changes** that reflect immediately when you save files.

## 🔧 **Method 1: Ionic Live Reload (Recommended)**

### **Step 1: Start Development Server with Live Reload**

Open **Terminal 1** and run:
```bash
ionic serve --host=0.0.0.0 --port=8100
```

This will:
- Start the development server on all network interfaces
- Make it accessible from your Android device
- Enable hot reload for instant changes

### **Step 2: Configure Capacitor for Live Reload**

Open **Terminal 2** and run:
```bash
npx cap run android --livereload --external --host=*************
```

**Important**: Replace `*************` with your actual IP address from `ipconfig`.

### **Step 3: Alternative Manual Setup**

If the automatic method doesn't work, manually configure:

1. **Start Ionic Server**:
   ```bash
   ionic serve --host=0.0.0.0 --port=8100
   ```

2. **Update capacitor.config.ts** temporarily:
   ```typescript
   const config: CapacitorConfig = {
     appId: 'io.ionic.starter',
     appName: 'pepouchdb',
     webDir: 'www',
     server: {
       url: 'http://*************:8100',
       cleartext: true
     },
     // ... rest of config
   };
   ```

3. **Sync and Run**:
   ```bash
   npx cap sync android
   npx cap run android
   ```

## 🔧 **Method 2: Android Studio Direct Development**

### **Step 1: Enable USB Debugging**

On your Android device:
1. Go to **Settings** → **About Phone**
2. Tap **Build Number** 7 times to enable Developer Options
3. Go to **Settings** → **Developer Options**
4. Enable **USB Debugging**
5. Connect device to computer via USB

### **Step 2: Set Up ADB (Android Debug Bridge)**

1. **Find Android SDK Path**:
   - Open Android Studio
   - Go to **File** → **Settings** → **Appearance & Behavior** → **System Settings** → **Android SDK**
   - Note the SDK path (usually `C:\Users\<USER>\AppData\Local\Android\Sdk`)

2. **Add ADB to PATH**:
   - Add `C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools` to your system PATH
   - Or use full path: `C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe devices`

### **Step 3: Verify Device Connection**

```bash
adb devices
```

Should show your connected device.

## 🚀 **Method 3: Complete Live Reload Workflow**

### **Terminal 1: Development Server**
```bash
cd "22-07-25\pepouchdb"
ionic serve --host=0.0.0.0 --port=8100
```

### **Terminal 2: Android Live Reload**
```bash
cd "22-07-25\pepouchdb"
npx cap run android --livereload --external --host=************* --port=8100
```

### **What This Does:**
1. **Starts Ionic Dev Server**: Serves your app with hot reload
2. **Configures Capacitor**: Points Android app to your dev server
3. **Builds and Deploys**: Installs app on Android device/emulator
4. **Live Reload**: Any file changes instantly reflect on Android

## 📱 **Method 4: Using Android Studio Directly**

### **Step 1: Open in Android Studio**
```bash
npx cap open android
```

### **Step 2: Configure Live Reload in Android Studio**

1. **Modify MainActivity.java** (if needed):
   ```java
   public class MainActivity extends BridgeActivity {
       @Override
       public void onCreate(Bundle savedInstanceState) {
           super.onCreate(savedInstanceState);
           
           // For development - point to local server
           // this.load("http://*************:8100");
       }
   }
   ```

2. **Or use capacitor.config.ts** (preferred):
   ```typescript
   server: {
     url: 'http://*************:8100',
     cleartext: true
   }
   ```

### **Step 3: Run from Android Studio**

1. **Start Ionic Server** first:
   ```bash
   ionic serve --host=0.0.0.0
   ```

2. **In Android Studio**:
   - Click **Run** button (green play icon)
   - Select your device/emulator
   - App will load from your development server

## 🔥 **Best Practices for Live Reload**

### **1. Network Requirements**
- Ensure your computer and Android device are on the **same Wi-Fi network**
- Disable any VPN that might block local network access
- Check firewall settings (Windows Defender might block port 8100)

### **2. Performance Tips**
- Use **USB connection** for faster deployment
- Use **Wi-Fi** for live reload (faster than USB for file changes)
- Close unnecessary apps on Android device for better performance

### **3. Debugging Setup**
- Enable **WebView debugging** in your app (already done in your config)
- Use **Chrome DevTools** for debugging:
  1. Open Chrome on computer
  2. Go to `chrome://inspect`
  3. Find your device and click "Inspect"

## 🛠️ **Troubleshooting**

### **Common Issues:**

1. **"Cannot connect to server"**:
   - Check IP address is correct
   - Ensure both devices on same network
   - Try disabling Windows Firewall temporarily

2. **"App not updating"**:
   - Clear app cache on Android
   - Restart development server
   - Force refresh in app (pull down)

3. **"ADB not found"**:
   - Install Android SDK Platform Tools
   - Add to system PATH
   - Use full path to adb.exe

### **Quick Commands Reference:**

```bash
# Start live reload development
ionic serve --host=0.0.0.0 --port=8100

# Run on Android with live reload
npx cap run android --livereload --external

# Open Android Studio
npx cap open android

# Check connected devices
adb devices

# Sync changes to Android
npx cap sync android

# Build and copy to Android
ionic build && npx cap copy android
```

## ✅ **Success Indicators**

When everything is working correctly, you should see:

1. **Ionic server running** on `http://*************:8100`
2. **Android app connects** to development server
3. **File changes** reflect immediately on Android device
4. **Console logs** visible in Chrome DevTools
5. **No build step** required for changes

This setup gives you the **fastest development experience** with instant feedback on your Android device!
