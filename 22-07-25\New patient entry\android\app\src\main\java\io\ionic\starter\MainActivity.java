package io.ionic.starter;

import android.os.Bundle;
import android.webkit.WebView;
import android.webkit.WebSettings;
import android.view.WindowManager;
import com.getcapacitor.BridgeActivity;

public class MainActivity extends BridgeActivity {
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Enable WebView debugging for development
        WebView.setWebContentsDebuggingEnabled(true);

        // Optimize WebView for better UI performance
        configureWebView();

        // Keep screen on during development (optional)
        // getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    }

    private void configureWebView() {
        // Get the WebView from Capacitor
        WebView webView = getBridge().getWebView();
        if (webView != null) {
            WebSettings settings = webView.getSettings();

            // Enable hardware acceleration (setRenderPriority is deprecated)
            // Hardware acceleration is enabled by default in modern WebView

            // Improve scrolling performance
            webView.setScrollBarStyle(WebView.SCROLLBARS_OUTSIDE_OVERLAY);
            webView.setScrollbarFadingEnabled(true);

            // Enable zoom controls (optional)
            settings.setBuiltInZoomControls(false);
            settings.setDisplayZoomControls(false);

            // Improve text rendering
            settings.setTextZoom(100);

            // Enable DOM storage for better app performance
            settings.setDomStorageEnabled(true);
            settings.setDatabaseEnabled(true);

            // Optimize caching
            settings.setCacheMode(WebSettings.LOAD_DEFAULT);
            // Note: setAppCacheEnabled() is deprecated and removed in API 33+

            // Better viewport handling
            settings.setUseWideViewPort(true);
            settings.setLoadWithOverviewMode(true);

            // Disable text selection (better for app-like experience)
            webView.setOnLongClickListener(v -> true);
        }
    }
}
