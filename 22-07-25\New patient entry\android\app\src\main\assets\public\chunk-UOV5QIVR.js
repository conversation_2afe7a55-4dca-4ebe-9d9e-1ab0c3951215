import{a as F,c as et,g as ie}from"./chunk-2R6CW7ES.js";var r={allRenderFn:!1,element:!0,event:!0,hasRenderFn:!0,hostListener:!0,hostListenerTargetWindow:!0,hostListenerTargetDocument:!0,hostListenerTargetBody:!0,hostListenerTargetParent:!1,hostListenerTarget:!0,member:!0,method:!0,mode:!0,observeAttribute:!0,prop:!0,propMutable:!0,reflect:!0,scoped:!0,shadowDom:!0,slot:!0,cssAnnotations:!0,state:!0,style:!0,formAssociated:!1,svg:!0,updatable:!0,vdomAttribute:!0,vdomXlink:!0,vdomClass:!0,vdomFunctional:!0,vdomKey:!0,vdomListener:!0,vdomRef:!0,vdomPropOrAttr:!0,vdomRender:!0,vdomStyle:!0,vdomText:!0,watchCallback:!0,taskQueue:!0,hotModuleReplacement:!1,isDebug:!1,isDev:!1,isTesting:!1,hydrateServerSide:!1,hydrateClientSide:!1,lifecycleDOMEvents:!1,lazyLoad:!1,profile:!1,slotRelocation:!0,appendChildSlotFix:!1,cloneNodeFix:!1,hydratedAttribute:!1,hydratedClass:!0,scriptDataOpts:!1,scopedSlotTextContentFix:!1,shadowDomShim:!1,slotChildNodesFix:!1,invisiblePrehydration:!0,propBoolean:!0,propNumber:!0,propString:!0,constructableCSS:!0,devTools:!1,shadowDelegatesFocus:!0,initializeNextTick:!1,asyncLoading:!0,asyncQueue:!1,transformTagName:!1,attachStyles:!0,experimentalSlotFixes:!1};var le="app";var tt="";var ns=et({});var rs=Object.defineProperty,os=(e,t)=>{for(var s in t)rs(e,s,{get:t[s],enumerable:!0})},lr={isDev:!!r.isDev,isBrowser:!0,isServer:!1,isTesting:!!r.isTesting},is="http://www.w3.org/2000/svg",ls="http://www.w3.org/1999/xhtml",xt=(e=>(e.Undefined="undefined",e.Null="null",e.String="string",e.Number="number",e.SpecialNumber="number",e.Boolean="boolean",e.BigInt="bigint",e))(xt||{}),Dt=(e=>(e.Array="array",e.Date="date",e.Map="map",e.Object="object",e.RegularExpression="regexp",e.Set="set",e.Channel="channel",e.Symbol="symbol",e))(Dt||{}),Ce="type",Ee="value",Ne="serialized:",as=(e,t)=>{var s;let n=t.$cmpMeta$;Object.entries((s=n.$members$)!=null?s:{}).map(([i,[d]])=>{if((r.state||r.prop)&&(d&31||d&32)){let l=e[i],c=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(e),i);Object.defineProperty(e,i,{get(){return c.get.call(this)},set(a){c.set.call(this,a)},configurable:!0,enumerable:!0}),e[i]=t.$instanceValues$.has(i)?t.$instanceValues$.get(i):l}})},E=e=>{if(e.__stencil__getHostRef)return e.__stencil__getHostRef()};var cs=(e,t)=>{let s={$flags$:0,$hostElement$:e,$cmpMeta$:t,$instanceValues$:new Map};r.isDev&&(s.$renderCount$=0),r.method&&r.lazyLoad&&(s.$onInstancePromise$=new Promise(o=>s.$onInstanceResolve$=o)),r.asyncLoading&&(s.$onReadyPromise$=new Promise(o=>s.$onReadyResolve$=o),e["s-p"]=[],e["s-rc"]=[]);let n=s;return e.__stencil__getHostRef=()=>n,!r.lazyLoad&&r.modernPropertyDecls&&(r.state||r.prop)&&as(e,s),n},st=(e,t)=>t in e,ds,U=(e,t)=>(ds||console.error)(e,t),It=r.isTesting?["STENCIL:"]:["%cstencil","color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px"],ue=(...e)=>console.error(...It,...e),J=(...e)=>console.warn(...It,...e);var nt=new Map;var $s=(e,t,s)=>{let n=e.$tagName$.replace(/-/g,"_"),o=e.$lazyBundleId$;if(r.isDev&&typeof o!="string"){ue(`Trying to lazily load component <${e.$tagName$}> with style mode "${t.$modeName$}", but it does not exist.`);return}else if(!o)return;let i=r.hotModuleReplacement?!1:nt.get(o);if(i)return i[n];return ns(`./${o}.entry.js${r.hotModuleReplacement&&s?"?s-hmr="+s:""}`).then(d=>(r.hotModuleReplacement||nt.set(o,d),d[n]),d=>{U(d,t.$hostElement$)})},he=new Map,Ct=[],fs="r",us="o",hs="s",ps="t",vs="c",Z="s-id",Y="sty-id",rt="c-id";var gs="slot-fb{display:contents}slot-fb[hidden]{display:none}",ot="http://www.w3.org/1999/xlink",ms=["formAssociatedCallback","formResetCallback","formDisabledCallback","formStateRestoreCallback"],y=typeof window<"u"?window:{},hr=y.HTMLElement||class{},m={$flags$:0,$resourcesUrl$:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,s,n)=>e.addEventListener(t,s,n),rel:(e,t,s,n)=>e.removeEventListener(t,s,n),ce:(e,t)=>new CustomEvent(e,t)};var K=r.shadowDom,ys=(()=>{var e;let t=!1;try{(e=y.document)==null||e.addEventListener("e",null,Object.defineProperty({},"passive",{get(){t=!0}}))}catch{}return t})(),Ss=e=>Promise.resolve(e),We=r.constructableCSS?(()=>{try{return new CSSStyleSheet,typeof new CSSStyleSheet().replaceSync=="function"}catch{}return!1})():!1,Ae=0,pe=!1,de=[],j=[],Te=[],Et=(e,t)=>s=>{e.push(s),pe||(pe=!0,t&&m.$flags$&4?be(ve):m.raf(ve))},it=e=>{for(let t=0;t<e.length;t++)try{e[t](performance.now())}catch(s){U(s)}e.length=0},lt=(e,t)=>{let s=0,n=0;for(;s<e.length&&(n=performance.now())<t;)try{e[s++](n)}catch(o){U(o)}s===e.length?e.length=0:s!==0&&e.splice(0,s)},ve=()=>{if(r.asyncQueue&&Ae++,it(de),r.asyncQueue){let e=(m.$flags$&6)===2?performance.now()+14*Math.ceil(Ae*.1):1/0;lt(j,e),lt(Te,e),j.length>0&&(Te.push(...j),j.length=0),(pe=de.length+j.length+Te.length>0)?m.raf(ve):Ae=0}else it(j),(pe=de.length>0)&&m.raf(ve)},be=e=>Ss().then(e),pr=Et(de,!1),_s=Et(j,!0);var bs=e=>e!=null&&e!==void 0,Me=e=>(e=typeof e,e==="object"||e==="function");function Ls(e){var t,s,n;return(n=(s=(t=e.head)==null?void 0:t.querySelector('meta[name="csp-nonce"]'))==null?void 0:s.getAttribute("content"))!=null?n:void 0}var At=e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),xs=class z{static fromLocalValue(t){let s=t[Ce],n=Ee in t?t[Ee]:void 0;switch(s){case"string":return n;case"boolean":return n;case"bigint":return BigInt(n);case"undefined":return;case"null":return null;case"number":return n==="NaN"?NaN:n==="-0"?-0:n==="Infinity"?1/0:n==="-Infinity"?-1/0:n;case"array":return n.map(a=>z.fromLocalValue(a));case"date":return new Date(n);case"map":let o=new Map;for(let[a,$]of n){let u=typeof a=="object"&&a!==null?z.fromLocalValue(a):a,f=z.fromLocalValue($);o.set(u,f)}return o;case"object":let i={};for(let[a,$]of n)i[a]=z.fromLocalValue($);return i;case"regexp":let{pattern:d,flags:l}=n;return new RegExp(d,l);case"set":let c=new Set;for(let a of n)c.add(z.fromLocalValue(a));return c;case"symbol":return Symbol(n);default:throw new Error(`Unsupported type: ${s}`)}}static fromLocalValueArray(t){return t.map(s=>z.fromLocalValue(s))}static isLocalValueObject(t){if(typeof t!="object"||t===null||!t.hasOwnProperty(Ce))return!1;let s=t[Ce];return Object.values(F(F({},xt),Dt)).includes(s)?s!=="null"&&s!=="undefined"?t.hasOwnProperty(Ee):!0:!1}},Ds={};os(Ds,{err:()=>Tt,map:()=>Is,ok:()=>Oe,unwrap:()=>Cs,unwrapErr:()=>Es});var Oe=e=>({isOk:!0,isErr:!1,value:e}),Tt=e=>({isOk:!1,isErr:!0,value:e});function Is(e,t){if(e.isOk){let s=t(e.value);return s instanceof Promise?s.then(n=>Oe(n)):Oe(s)}if(e.isErr){let s=e.value;return Tt(s)}throw"should never get here"}var Cs=e=>{if(e.isOk)return e.value;throw e.value},Es=e=>{if(e.isErr)return e.value;throw e.value};function As(e){return typeof e!="string"||!e.startsWith(Ne)?e:xs.fromLocalValue(JSON.parse(atob(e.slice(Ne.length))))}function Ts(e){let t=r.shadowDelegatesFocus?this.attachShadow({mode:"open",delegatesFocus:!!(e.$flags$&16)}):this.attachShadow({mode:"open"});if(We){let s=new CSSStyleSheet;s.replaceSync(tt),t.adoptedStyleSheets.push(s)}}var se=e=>t=>e(t.toLowerCase()),ks=se(e=>e.endsWith(".d.ts")||e.endsWith(".d.mts")||e.endsWith(".d.cts")),yr=se(e=>!ks(e)&&(e.endsWith(".ts")||e.endsWith(".mts")||e.endsWith(".cts"))),Sr=se(e=>e.endsWith(".tsx")||e.endsWith(".mtsx")||e.endsWith(".ctsx")),_r=se(e=>e.endsWith(".jsx")||e.endsWith(".mjsx")||e.endsWith(".cjsx")),br=se(e=>e.endsWith(".js")||e.endsWith(".mjs")||e.endsWith(".cjs")),Le=e=>{let t=I(e,"childNodes");e.tagName&&e.tagName.includes("-")&&e["s-cr"]&&e.tagName!=="SLOT-FB"&&ne(t,e.tagName).forEach(n=>{n.nodeType===1&&n.tagName==="SLOT-FB"&&(qe(n,Q(n),!1).length?n.hidden=!0:n.hidden=!1)});let s=0;for(s=0;s<t.length;s++){let n=t[s];n.nodeType===1&&I(n,"childNodes").length&&Le(n)}},V=e=>{let t=[];for(let s=0;s<e.length;s++){let n=e[s]["s-nr"]||void 0;n&&n.isConnected&&t.push(n)}return t};function ne(e,t,s){let n=0,o=[],i;for(;n<e.length;n++){if(i=e[n],i["s-sr"]&&(!t||i["s-hn"]===t)&&(s===void 0||Q(i)===s)&&(o.push(i),typeof s<"u"))return o;o=[...o,...ne(i.childNodes,t,s)]}return o}var qe=(e,t,s=!0)=>{let n=[];(s&&e["s-sr"]||!e["s-sr"])&&n.push(e);let o=e;for(;o=o.nextSibling;)Q(o)===t&&(s||!o["s-sr"])&&n.push(o);return n},at=(e,t)=>e.nodeType===1?e.getAttribute("slot")===null&&t===""||e.getAttribute("slot")===t:e["s-sn"]===t?!0:t==="",xe=(e,t,s,n)=>{if(e["s-ol"]&&e["s-ol"].isConnected)return;let o=document.createTextNode("");if(o["s-nr"]=e,!t["s-cr"]||!t["s-cr"].parentNode)return;let i=t["s-cr"].parentNode,d=s?I(i,"prepend"):I(i,"appendChild");if(r.hydrateClientSide&&typeof n<"u"){o["s-oo"]=n;let l=I(i,"childNodes"),c=[o];l.forEach(a=>{a["s-nr"]&&c.push(a)}),c.sort((a,$)=>!a["s-oo"]||a["s-oo"]<($["s-oo"]||0)?-1:!$["s-oo"]||$["s-oo"]<a["s-oo"]?1:0),c.forEach(a=>d.call(i,a))}else d.call(i,o);e["s-ol"]=o,e["s-sh"]=t["s-hn"]},Q=e=>typeof e["s-sn"]=="string"?e["s-sn"]:e.nodeType===1&&e.getAttribute("slot")||void 0;function kt(e){if(e.assignedElements||e.assignedNodes||!e["s-sr"])return;let t=s=>(function(n){let o=[],i=this["s-sn"];n?.flatten&&console.error(`
          Flattening is not supported for Stencil non-shadow slots. 
          You can use \`.childNodes\` to nested slot fallback content.
          If you have a particular use case, please open an issue on the Stencil repo.
        `);let d=this["s-cr"].parentElement;return(d.__childNodes?d.childNodes:V(d.childNodes)).forEach(c=>{i===Q(c)&&o.push(c)}),s?o.filter(c=>c.nodeType===1):o}).bind(e);e.assignedElements=t(!0),e.assignedNodes=t(!1)}function De(e){e.dispatchEvent(new CustomEvent("slotchange",{bubbles:!1,cancelable:!1,composed:!1}))}function Ke(e,t){var s;if(t=t||((s=e["s-ol"])==null?void 0:s.parentElement),!t)return{slotNode:null,slotName:""};let n=e["s-sn"]=Q(e)||"",o=I(t,"childNodes");return{slotNode:ne(o,t.tagName,n)[0],slotName:n}}var ws=e=>{wt(e),Nt(e),Rs(e),Os(e),zs(e),Bs(e),Us(e),js(e),Ot(e),Rt(e),Ns(e)},wt=e=>{let t=e.cloneNode;e.cloneNode=function(s){let n=this,o=r.shadowDom?n.shadowRoot&&K:!1,i=t.call(n,o?s:!1);if(r.slot&&!o&&s){let d=0,l,c,a=["s-id","s-cr","s-lr","s-rc","s-sc","s-p","s-cn","s-sr","s-sn","s-hn","s-ol","s-nr","s-si","s-rf","s-scs"],$=this.__childNodes||this.childNodes;for(;d<$.length;d++)l=$[d]["s-nr"],c=a.every(u=>!$[d][u]),l&&(r.appendChildSlotFix&&i.__appendChild?i.__appendChild(l.cloneNode(!0)):i.appendChild(l.cloneNode(!0))),c&&i.appendChild($[d].cloneNode(!0))}return i}},Nt=e=>{e.__appendChild=e.appendChild,e.appendChild=function(t){let{slotName:s,slotNode:n}=Ke(t,this);if(n){xe(t,n);let o=qe(n,s),i=o[o.length-1],d=I(i,"parentNode"),l=I(d,"insertBefore")(t,i.nextSibling);return De(n),Le(this),l}return this.__appendChild(t)}},Ns=e=>{e.__removeChild=e.removeChild,e.removeChild=function(t){if(t&&typeof t["s-sn"]<"u"){let s=this.__childNodes||this.childNodes;if(ne(s,this.tagName,t["s-sn"])&&t.isConnected){t.remove(),Le(this);return}}return this.__removeChild(t)}},Os=e=>{e.__prepend=e.prepend,e.prepend=function(...t){t.forEach(s=>{typeof s=="string"&&(s=this.ownerDocument.createTextNode(s));let n=(s["s-sn"]=Q(s))||"",o=I(this,"childNodes"),i=ne(o,this.tagName,n)[0];if(i){xe(s,i,!0);let l=qe(i,n)[0],c=I(l,"parentNode"),a=I(c,"insertBefore")(s,I(l,"nextSibling"));return De(i),a}return s.nodeType===1&&s.getAttribute("slot")&&(s.hidden=!0),e.__prepend(s)})}},Rs=e=>{e.__append=e.append,e.append=function(...t){t.forEach(s=>{typeof s=="string"&&(s=this.ownerDocument.createTextNode(s)),this.appendChild(s)})}},Bs=e=>{let t=e.insertAdjacentHTML;e.insertAdjacentHTML=function(s,n){if(s!=="afterbegin"&&s!=="beforeend")return t.call(this,s,n);let o=this.ownerDocument.createElement("_"),i;if(o.innerHTML=n,s==="afterbegin")for(;i=o.firstChild;)this.prepend(i);else if(s==="beforeend")for(;i=o.firstChild;)this.append(i)}},Us=e=>{e.insertAdjacentText=function(t,s){this.insertAdjacentHTML(t,s)}},js=e=>{let t=e;t.__insertBefore||(t.__insertBefore=e.insertBefore,e.insertBefore=function(s,n){let{slotName:o,slotNode:i}=Ke(s,this),d=this.__childNodes?this.childNodes:V(this.childNodes);if(i){let c=!1;if(d.forEach(a=>{if(a===n||n===null){if(c=!0,n===null||o!==n["s-sn"]){this.appendChild(s);return}if(o===n["s-sn"]){xe(s,i);let $=I(n,"parentNode");I($,"insertBefore")(s,n),De(i)}return}}),c)return s}let l=n?.__parentNode;return l&&!this.isSameNode(l)?this.appendChild(s):this.__insertBefore(s,n)})},zs=e=>{let t=e.insertAdjacentElement;e.insertAdjacentElement=function(s,n){return s!=="afterbegin"&&s!=="beforeend"?t.call(this,s,n):s==="afterbegin"?(this.prepend(n),n):(s==="beforeend"&&this.append(n),n)}},Ot=e=>{T("textContent",e),Object.defineProperty(e,"textContent",{get:function(){let t="";return(this.__childNodes?this.childNodes:V(this.childNodes)).forEach(n=>t+=n.textContent||""),t},set:function(t){(this.__childNodes?this.childNodes:V(this.childNodes)).forEach(n=>{n["s-ol"]&&n["s-ol"].remove(),n.remove()}),this.insertAdjacentHTML("beforeend",t)}})},Rt=e=>{class t extends Array{item(n){return this[n]}}T("children",e),Object.defineProperty(e,"children",{get(){return this.childNodes.filter(s=>s.nodeType===1)}}),Object.defineProperty(e,"childElementCount",{get(){return this.children.length}}),T("firstChild",e),Object.defineProperty(e,"firstChild",{get(){return this.childNodes[0]}}),T("lastChild",e),Object.defineProperty(e,"lastChild",{get(){return this.childNodes[this.childNodes.length-1]}}),T("childNodes",e),Object.defineProperty(e,"childNodes",{get(){let s=new t;return s.push(...V(this.__childNodes)),s}})},Ps=e=>{!e||e.__nextSibling!==void 0||!globalThis.Node||(Hs(e),Ws(e),Bt(e),e.nodeType===Node.ELEMENT_NODE&&(Fs(e),Ms(e)))},Hs=e=>{!e||e.__nextSibling||(T("nextSibling",e),Object.defineProperty(e,"nextSibling",{get:function(){var t;let s=(t=this["s-ol"])==null?void 0:t.parentNode.childNodes,n=s?.indexOf(this);return s&&n>-1?s[n+1]:this.__nextSibling}}))},Fs=e=>{!e||e.__nextElementSibling||(T("nextElementSibling",e),Object.defineProperty(e,"nextElementSibling",{get:function(){var t;let s=(t=this["s-ol"])==null?void 0:t.parentNode.children,n=s?.indexOf(this);return s&&n>-1?s[n+1]:this.__nextElementSibling}}))},Ws=e=>{!e||e.__previousSibling||(T("previousSibling",e),Object.defineProperty(e,"previousSibling",{get:function(){var t;let s=(t=this["s-ol"])==null?void 0:t.parentNode.childNodes,n=s?.indexOf(this);return s&&n>-1?s[n-1]:this.__previousSibling}}))},Ms=e=>{!e||e.__previousElementSibling||(T("previousElementSibling",e),Object.defineProperty(e,"previousElementSibling",{get:function(){var t;let s=(t=this["s-ol"])==null?void 0:t.parentNode.children,n=s?.indexOf(this);return s&&n>-1?s[n-1]:this.__previousElementSibling}}))},Bt=e=>{!e||e.__parentNode||(T("parentNode",e),Object.defineProperty(e,"parentNode",{get:function(){var t;return((t=this["s-ol"])==null?void 0:t.parentNode)||this.__parentNode},set:function(t){this.__parentNode=t}}))},qs=["children","nextElementSibling","previousElementSibling"],Ks=["childNodes","firstChild","lastChild","nextSibling","previousSibling","textContent","parentNode"];function T(e,t){let s;qs.includes(e)?s=Object.getOwnPropertyDescriptor(Element.prototype,e):Ks.includes(e)&&(s=Object.getOwnPropertyDescriptor(Node.prototype,e)),s||(s=Object.getOwnPropertyDescriptor(t,e)),s&&Object.defineProperty(t,"__"+e,s)}function I(e,t){if("__"+t in e){let s=e["__"+t];return typeof s!="function"?s:s.bind(e)}else return typeof e[t]!="function"?e[t]:e[t].bind(e)}var Xs=0,B=(e,t="")=>{if(r.profile&&performance.mark){let s=`st:${e}:${t}:${Xs++}`;return performance.mark(s),()=>performance.measure(`[Stencil] ${e}() <${t}>`,s)}else return()=>{}},Qs=(e,t)=>r.profile&&performance.mark?(performance.getEntriesByName(e,"mark").length===0&&performance.mark(e),()=>{performance.getEntriesByName(t,"measure").length===0&&performance.measure(t,e)}):()=>{};var Ut=(e,t,...s)=>{let n=null,o=null,i=null,d=!1,l=!1,c=[],a=u=>{for(let f=0;f<u.length;f++)n=u[f],Array.isArray(n)?a(n):n!=null&&typeof n!="boolean"&&((d=typeof e!="function"&&!Me(n))?n=String(n):r.isDev&&typeof e!="function"&&n.$flags$===void 0&&ue(`vNode passed as children has unexpected type.
Make sure it's using the correct h() function.
Empty objects can also be the cause, look for JSX comments that became objects.`),d&&l?c[c.length-1].$text$+=n:c.push(d?X(null,n):n),l=d)};if(a(s),t&&(r.isDev&&e==="input"&&Zs(t),r.vdomKey&&t.key&&(o=t.key),r.slotRelocation&&t.name&&(i=t.name),r.vdomClass)){let u=t.className||t.class;u&&(t.class=typeof u!="object"?u:Object.keys(u).filter(f=>u[f]).join(" "))}if(r.isDev&&c.some(Re)&&ue(`The <Host> must be the single root component. Make sure:
- You are NOT using hostData() and <Host> in the same component.
- <Host> is used once, and it's the single root component of the render() function.`),r.vdomFunctional&&typeof e=="function")return e(t===null?{}:t,c,Gs);let $=X(e,null);return $.$attrs$=t,c.length>0&&($.$children$=c),r.vdomKey&&($.$key$=o),r.slotRelocation&&($.$name$=i),$},X=(e,t)=>{let s={$flags$:0,$tag$:e,$text$:t,$elm$:null,$children$:null};return r.vdomAttribute&&(s.$attrs$=null),r.vdomKey&&(s.$key$=null),r.slotRelocation&&(s.$name$=null),s},Ys={},Re=e=>e&&e.$tag$===Ys,Gs={forEach:(e,t)=>e.map(ct).forEach(t),map:(e,t)=>e.map(ct).map(t).map(Js)},ct=e=>({vattrs:e.$attrs$,vchildren:e.$children$,vkey:e.$key$,vname:e.$name$,vtag:e.$tag$,vtext:e.$text$}),Js=e=>{if(typeof e.vtag=="function"){let s=F({},e.vattrs);return e.vkey&&(s.key=e.vkey),e.vname&&(s.name=e.vname),Ut(e.vtag,s,...e.vchildren||[])}let t=X(e.vtag,e.vtext);return t.$attrs$=e.vattrs,t.$children$=e.vchildren,t.$key$=e.vkey,t.$name$=e.vname,t},Zs=e=>{let t=Object.keys(e),s=t.indexOf("value");if(s===-1)return;let n=t.indexOf("type"),o=t.indexOf("min"),i=t.indexOf("max"),d=t.indexOf("step");(s<n||s<o||s<i||s<d)&&J('The "value" prop of <input> should be set after "min", "max", "type" and "step"')},Vs=(e,t,s,n)=>{var o;let i=B("hydrateClient",t),d=e.shadowRoot,l=[],c=[],a=[],$=r.shadowDom&&d?[]:null,u=X(t,null);u.$elm$=e,Object.entries(((o=n.$cmpMeta$)==null?void 0:o.$members$)||{}).forEach(([S,[C,b]])=>{var oe;if(!(C&31))return;let ts=b||S,Ve=e.getAttribute(ts);if(Ve!==null){let ss=me(Ve,C);(oe=n?.$instanceValues$)==null||oe.set(S,ss)}});let p;if(r.scoped){let S=n.$cmpMeta$;S&&S.$flags$&10&&e["s-sc"]?(p=e["s-sc"],e.classList.add(p+"-h")):e["s-sc"]&&delete e["s-sc"]}y.document&&(!m.$orgLocNodes$||!m.$orgLocNodes$.size)&&Ue(y.document.body,m.$orgLocNodes$=new Map),e[Z]=s,e.removeAttribute(Z),n.$vnode$=Be(u,l,c,$,e,e,s,a);let h=0,v=l.length,g;for(h;h<v;h++){g=l[h];let S=g.$hostId$+"."+g.$nodeId$,C=m.$orgLocNodes$.get(S),b=g.$elm$;d||(b["s-hn"]=t.toUpperCase(),g.$tag$==="slot"&&(b["s-cr"]=e["s-cr"])),g.$tag$==="slot"&&(g.$name$=g.$elm$["s-sn"]||g.$elm$.name||null,g.$children$?(g.$flags$|=2,g.$elm$.childNodes.length||g.$children$.forEach(oe=>{g.$elm$.appendChild(oe.$elm$)})):g.$flags$|=1),C&&C.isConnected&&(d&&C["s-en"]===""&&C.parentNode.insertBefore(b,C.nextSibling),C.parentNode.removeChild(C),d||(b["s-oo"]=parseInt(g.$nodeId$))),m.$orgLocNodes$.delete(S)}let L=[],H=a.length,w=0,A,_,Ze,x;for(w;w<H;w++)if(A=a[w],!(!A||!A.length))for(Ze=A.length,_=0,_;_<Ze;_++){if(x=A[_],L[x.hostId]||(L[x.hostId]=m.$orgLocNodes$.get(x.hostId)),!L[x.hostId])continue;let S=L[x.hostId];(!S.shadowRoot||!d)&&(x.slot["s-cr"]=S["s-cr"],!x.slot["s-cr"]&&S.shadowRoot?x.slot["s-cr"]=S:x.slot["s-cr"]=(S.__childNodes||S.childNodes)[0],xe(x.node,x.slot,!1,x.node["s-oo"]),r.experimentalSlotFixes&&Ps(x.node)),S.shadowRoot&&x.node.parentElement!==S&&S.appendChild(x.node)}if(r.scoped&&p&&c.length&&c.forEach(S=>{S.$elm$.parentElement.classList.add(p+"-s")}),r.shadowDom&&d&&!d.childNodes.length){let S=0,C=$.length;if(C){for(S;S<C;S++)d.appendChild($[S]);Array.from(e.childNodes).forEach(b=>{typeof b["s-sn"]!="string"&&(b.nodeType===1&&b.slot&&b.hidden?b.removeAttribute("hidden"):(b.nodeType===8||b.nodeType===3&&!b.wholeText.trim())&&b.parentNode.removeChild(b))})}}m.$orgLocNodes$.delete(e["s-id"]),n.$hostElement$=e,i()},Be=(e,t,s,n,o,i,d,l=[])=>{let c,a,$,u,f=o["s-sc"];if(i.nodeType===1){if(c=i.getAttribute(rt),c&&(a=c.split("."),a[0]===d||a[0]==="0")){$=dt({$flags$:0,$hostId$:a[0],$nodeId$:a[1],$depth$:a[2],$index$:a[3],$tag$:i.tagName.toLowerCase(),$elm$:i,$attrs$:{class:i.className||""}}),t.push($),i.removeAttribute(rt),e.$children$||(e.$children$=[]),r.scoped&&f&&(i["s-si"]=f,$.$attrs$.class+=" "+f);let h=$.$elm$.getAttribute("s-sn");typeof h=="string"&&($.$tag$==="slot-fb"&&($t(h,a[2],$,i,e,t,s,n,l),r.scoped&&f&&i.classList.add(f)),$.$elm$["s-sn"]=h,$.$elm$.removeAttribute("s-sn")),$.$index$!==void 0&&(e.$children$[$.$index$]=$),e=$,n&&$.$depth$==="0"&&(n[$.$index$]=$.$elm$)}if(i.shadowRoot)for(u=i.shadowRoot.childNodes.length-1;u>=0;u--)Be(e,t,s,n,o,i.shadowRoot.childNodes[u],d,l);let p=i.__childNodes||i.childNodes;for(u=p.length-1;u>=0;u--)Be(e,t,s,n,o,p[u],d,l)}else if(i.nodeType===8){if(a=i.nodeValue.split("."),a[1]===d||a[1]==="0"){if(c=a[0],$=dt({$hostId$:a[1],$nodeId$:a[2],$depth$:a[3],$index$:a[4]||"0",$elm$:i,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null}),c===ps)$.$elm$=ut(i,3),$.$elm$&&$.$elm$.nodeType===3&&($.$text$=$.$elm$.textContent,t.push($),i.remove(),d===$.$hostId$&&(e.$children$||(e.$children$=[]),e.$children$[$.$index$]=$),n&&$.$depth$==="0"&&(n[$.$index$]=$.$elm$));else if(c===vs)$.$elm$=ut(i,8),$.$elm$&&$.$elm$.nodeType===8&&(t.push($),i.remove());else if($.$hostId$===d)if(c===hs){let p=i["s-sn"]=a[5]||"";$t(p,a[2],$,i,e,t,s,n,l)}else c===fs&&(r.shadowDom&&n?i.remove():r.slotRelocation&&(o["s-cr"]=i,i["s-cn"]=!0))}}else if(e&&e.$tag$==="style"){let p=X(null,i.textContent);p.$elm$=i,p.$index$="0",e.$children$=[p]}else i.nodeType===3&&!i.wholeText.trim()&&i.remove();return e},Ue=(e,t)=>{if(e.nodeType===1){let s=e[Z]||e.getAttribute(Z);s&&t.set(s,e);let n=0;if(e.shadowRoot)for(;n<e.shadowRoot.childNodes.length;n++)Ue(e.shadowRoot.childNodes[n],t);let o=e.__childNodes||e.childNodes;for(n=0;n<o.length;n++)Ue(o[n],t)}else if(e.nodeType===8){let s=e.nodeValue.split(".");s[0]===us&&(t.set(s[1]+"."+s[2],e),e.nodeValue="",e["s-en"]=s[3])}},dt=e=>F(F({},{$flags$:0,$hostId$:null,$nodeId$:null,$depth$:null,$index$:"0",$elm$:null,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null}),e);function $t(e,t,s,n,o,i,d,l,c){n["s-sr"]=!0,s.$name$=e||null,s.$tag$="slot";let a=o?.$elm$?o.$elm$["s-id"]||o.$elm$.getAttribute("s-id"):"";if(r.shadowDom&&l&&y.document){let $=s.$elm$=y.document.createElement(s.$tag$);s.$name$&&s.$elm$.setAttribute("name",e),a&&a!==s.$hostId$?o.$elm$.insertBefore($,o.$elm$.children[0]):n.parentNode.insertBefore(s.$elm$,n),ft(c,t,e,n,s.$hostId$),n.remove(),s.$depth$==="0"&&(l[s.$index$]=s.$elm$)}else{let $=s.$elm$,u=a&&a!==s.$hostId$&&o.$elm$.shadowRoot;ft(c,t,e,n,u?a:s.$hostId$),kt(n),u&&o.$elm$.insertBefore($,o.$elm$.children[0]),i.push(s)}d.push(s),o.$children$||(o.$children$=[]),o.$children$[s.$index$]=s}var ft=(e,t,s,n,o)=>{let i=n.nextSibling;for(e[t]=e[t]||[];i&&((i.getAttribute&&i.getAttribute("slot")||i["s-sn"])===s||s===""&&!i["s-sn"]&&(i.nodeType===8&&i.nodeValue.indexOf(".")!==1||i.nodeType===3));)i["s-sn"]=s,e[t].push({slot:n,node:i,hostId:o}),i=i.nextSibling},ut=(e,t)=>{let s=e;do s=s.nextSibling;while(s&&(s.nodeType!==t||!s.nodeValue));return s};var en=e=>{let t=[],s=0;return e=e.replace(/(\[[^\]]*\])/g,(i,d)=>{let l=`__ph-${s}__`;return t.push(d),s++,l}),{content:e.replace(/(:nth-[-\w]+)(\([^)]+\))/g,(i,d,l)=>{let c=`__ph-${s}__`;return t.push(l),s++,d+c}),placeholders:t}},tn=(e,t)=>t.replace(/__ph-(\d+)__/g,(s,n)=>e[+n]),re="-shadowcsshost",jt="-shadowcssslotted",zt="-shadowcsscontext",Xe=")(?:\\(((?:\\([^)(]*\\)|[^)(]*)+?)\\))?([^,{]*)",sn=new RegExp("("+re+Xe,"gim"),nn=new RegExp("("+zt+Xe,"gim"),rn=new RegExp("("+jt+Xe,"gim"),P=re+"-no-combinator",on=/-shadowcsshost-no-combinator([^\s]*)/,ln=[/::shadow/g,/::content/g],an="([>\\s~+[.,{:][\\s\\S]*)?$",$e=/-shadowcsshost/gim,Qe=e=>{let t=At(e);return new RegExp(`(^|[^@]|@(?!supports\\s+selector\\s*\\([^{]*?${t}))(${t}\\b)`,"g")},cn=Qe("::slotted"),dn=Qe(":host"),$n=Qe(":host-context"),fn=/\/\*\s*[\s\S]*?\*\//g,un=e=>e.replace(fn,""),hn=/\/\*\s*#\s*source(Mapping)?URL=[\s\S]+?\*\//g,pn=e=>e.match(hn)||[],vn=/(\s*)([^;\{\}]+?)(\s*)((?:{%BLOCK%}?\s*;?)|(?:\s*;))/g,gn=/([{}])/g,mn=/(^.*?[^\\])??((:+)(.*)|$)/,yn="{",Sn="}",ge="%BLOCK%",je=(e,t)=>{let s=_n(e),n=0;return s.escapedString.replace(vn,(...o)=>{let i=o[2],d="",l=o[4],c="";l&&l.startsWith("{"+ge)&&(d=s.blocks[n++],l=l.substring(ge.length+1),c="{");let $=t({selector:i,content:d});return`${o[1]}${$.selector}${o[3]}${c}${$.content}${l}`})},_n=e=>{let t=e.split(gn),s=[],n=[],o=0,i=[];for(let l=0;l<t.length;l++){let c=t[l];c===Sn&&o--,o>0?i.push(c):(i.length>0&&(n.push(i.join("")),s.push(ge),i=[]),s.push(c)),c===yn&&o++}return i.length>0&&(n.push(i.join("")),s.push(ge)),{escapedString:s.join(""),blocks:n}},bn=e=>{let t=[];return e=e.replace(/@supports\s+selector\s*\(\s*([^)]*)\s*\)/g,(s,n)=>{let o=`__supports_${t.length}__`;return t.push(n),`@supports selector(${o})`}),e=e.replace($n,`$1${zt}`).replace(dn,`$1${re}`).replace(cn,`$1${jt}`),t.forEach((s,n)=>{e=e.replace(`__supports_${n}__`,s)}),e},Pt=(e,t,s)=>e.replace(t,(...n)=>{if(n[2]){let o=n[2].split(","),i=[];for(let d=0;d<o.length;d++){let l=o[d].trim();if(!l)break;i.push(s(P,l,n[3]))}return i.join(",")}else return P+n[3]}),Ht=(e,t,s)=>e+t.replace(re,"")+s,Ln=e=>Pt(e,sn,Ht),xn=(e,t,s)=>t.indexOf(re)>-1?Ht(e,t,s):e+t+s+", "+t+" "+e+s,Dn=(e,t)=>{let s="."+t+" > ",n=[];return e=e.replace(rn,(...o)=>{if(o[2]){let i=o[2].trim(),d=o[3],l=s+i+d,c="";for(let u=o[4]-1;u>=0;u--){let f=o[5][u];if(f==="}"||f===",")break;c=f+c}let a=(c+l).trim(),$=`${c.trimEnd()}${l.trim()}`.trim();if(a!==$){let u=`${$}, ${a}`;n.push({orgSelector:a,updatedSelector:u})}return l}else return P+o[3]}),{selectors:n,cssText:e}},In=e=>Pt(e,nn,xn),Cn=e=>ln.reduce((t,s)=>t.replace(s," "),e),En=e=>{let t=/\[/g,s=/\]/g;return e=e.replace(t,"\\[").replace(s,"\\]"),new RegExp("^("+e+")"+an,"m")},An=(e,t)=>!En(t).test(e),Ft=(e,t)=>e.replace(mn,(s,n="",o,i="",d="")=>n+t+i+d),Tn=(e,t,s)=>{if($e.lastIndex=0,$e.test(e)){let n=`.${s}`;return e.replace(on,(o,i)=>Ft(i,n)).replace($e,n+" ")}return t+" "+e},kn=(e,t,s)=>{let n=/\[is=([^\]]*)\]/g;t=t.replace(n,(h,...v)=>v[0]);let o="."+t,i=h=>{let v=h.trim();if(!v)return"";if(h.indexOf(P)>-1)v=Tn(h,t,s);else{let g=h.replace($e,"");g.length>0&&(v=Ft(g,o))}return v},d=en(e);e=d.content;let l="",c=0,a,$=/( |>|\+|~(?!=))\s*/g,f=!(e.indexOf(P)>-1);for(;(a=$.exec(e))!==null;){let h=a[1],v=e.slice(c,a.index).trim();f=f||v.indexOf(P)>-1;let g=f?i(v):v;l+=`${g} ${h} `,c=$.lastIndex}let p=e.substring(c);return f=f||p.indexOf(P)>-1,l+=f?i(p):p,tn(d.placeholders,l)},wn=(e,t,s,n)=>e.split(",").map(o=>n&&o.indexOf("."+n)>-1?o.trim():An(o,t)?kn(o,t,s).trim():o.trim()).join(", "),Wt=(e,t,s,n,o)=>je(e,i=>{let d=i.selector,l=i.content;return i.selector[0]!=="@"?d=wn(i.selector,t,s,n):(i.selector.startsWith("@media")||i.selector.startsWith("@supports")||i.selector.startsWith("@page")||i.selector.startsWith("@document"))&&(l=Wt(i.content,t,s,n,o)),{selector:d.replace(/\s{2,}/g," ").trim(),content:l}}),Nn=(e,t,s,n,o)=>{e=bn(e),e=Ln(e),e=In(e);let i=Dn(e,n);return e=i.cssText,e=Cn(e),t&&(e=Wt(e,t,s,n,o)),e=ke(e,s),e=e.replace(/>\s*\*\s+([^{, ]+)/gm," $1 "),{cssText:e.trim(),slottedSelectors:i.selectors.map(d=>({orgSelector:ke(d.orgSelector,s),updatedSelector:ke(d.updatedSelector,s)}))}},ke=(e,t)=>e.replace(/-shadowcsshost-no-combinator/g,`.${t}`),On=(e,t,s)=>{let n=t+"-h",o=t+"-s",i=pn(e);e=un(e);let d=[];if(s){let c=a=>{let $=`/*!@___${d.length}___*/`,u=`/*!@${a.selector}*/`;return d.push({placeholder:$,comment:u}),a.selector=$+a.selector,a};e=je(e,a=>a.selector[0]!=="@"?c(a):((a.selector.startsWith("@media")||a.selector.startsWith("@supports")||a.selector.startsWith("@page")||a.selector.startsWith("@document"))&&(a.content=je(a.content,c)),a))}let l=Nn(e,t,n,o,s);return e=[l.cssText,...i].join(`
`),s&&d.forEach(({placeholder:c,comment:a})=>{e=e.replace(c,a)}),l.slottedSelectors.forEach(c=>{let a=new RegExp(At(c.orgSelector),"g");e=e.replace(a,c.updatedSelector)}),e},Rn=e=>Ct.map(t=>t(e)).find(t=>!!t),Tr=e=>Ct.push(e),kr=e=>E(e).$modeName$,me=(e,t)=>{if((r.hydrateClientSide||r.hydrateServerSide)&&typeof e=="string"&&(e.startsWith("{")&&e.endsWith("}")||e.startsWith("[")&&e.endsWith("]")))try{return e=JSON.parse(e),e}catch{}return(r.hydrateClientSide||r.hydrateServerSide)&&typeof e=="string"&&e.startsWith(Ne)?(e=As(e),e):e!=null&&!Me(e)?r.propBoolean&&t&4?e==="false"?!1:e===""||!!e:r.propNumber&&t&2?typeof e=="string"?parseFloat(e):typeof e=="number"?e:NaN:r.propString&&t&1?String(e):e:e},Bn=e=>r.lazyLoad?E(e).$hostElement$:e,jr=(e,t,s)=>{let n=Bn(e);return{emit:o=>(r.isDev&&!n.isConnected&&J(`The "${t}" event was emitted, but the dispatcher node is no longer connected to the dom.`),Ye(n,t,{bubbles:!!(s&4),composed:!!(s&2),cancelable:!!(s&1),detail:o}))}},Ye=(e,t,s)=>{let n=m.ce(t,s);return e.dispatchEvent(n),n},M=new WeakMap,Mt=(e,t,s)=>{let n=he.get(e);We&&s?(n=n||new CSSStyleSheet,typeof n=="string"?n=t:n.replaceSync(t)):n=t,he.set(e,n)},ze=(e,t,s)=>{var n;let o=Ge(t,s),i=he.get(o);if(!r.attachStyles||!y.document)return o;if(e=e.nodeType===11?e:y.document,i)if(typeof i=="string"){e=e.head||e;let d=M.get(e),l;if(d||M.set(e,d=new Set),!d.has(o)){if(r.hydrateClientSide&&e.host&&(l=e.querySelector(`[${Y}="${o}"]`)))l.innerHTML=i;else{l=document.querySelector(`[${Y}="${o}"]`)||y.document.createElement("style"),l.innerHTML=i;let c=(n=m.$nonce$)!=null?n:Ls(y.document);if(c!=null&&l.setAttribute("nonce",c),(r.hydrateServerSide||r.hotModuleReplacement)&&(t.$flags$&2||t.$flags$&128)&&l.setAttribute(Y,o),!(t.$flags$&1))if(e.nodeName==="HEAD"){let a=e.querySelectorAll("link[rel=preconnect]"),$=a.length>0?a[a.length-1].nextSibling:e.querySelector("style");e.insertBefore(l,$?.parentNode===e?$:null)}else if("host"in e)if(We){let a=new CSSStyleSheet;a.replaceSync(i),e.adoptedStyleSheets=[a,...e.adoptedStyleSheets]}else{let a=e.querySelector("style");a?a.innerHTML=i+a.innerHTML:e.prepend(l)}else e.append(l);t.$flags$&1&&e.insertBefore(l,null)}t.$flags$&4&&(l.innerHTML+=gs),d&&d.add(o)}}else r.constructableCSS&&!e.adoptedStyleSheets.includes(i)&&(e.adoptedStyleSheets=[...e.adoptedStyleSheets,i]);return o},Un=e=>{let t=e.$cmpMeta$,s=e.$hostElement$,n=t.$flags$,o=B("attachStyles",t.$tagName$),i=ze(r.shadowDom&&K&&s.shadowRoot?s.shadowRoot:s.getRootNode(),t,e.$modeName$);(r.shadowDom||r.scoped)&&r.cssAnnotations&&n&10&&(s["s-sc"]=i,s.classList.add(i+"-h")),o()},Ge=(e,t)=>"sc-"+(r.mode&&t&&e.$flags$&32?e.$tagName$+"-"+t:e.$tagName$),jn=e=>e.replace(/\/\*!@([^\/]+)\*\/[^\{]+\{/g,"$1{"),zn=()=>{if(!y.document)return;let e=y.document.querySelectorAll(`[${Y}]`),t=0;for(;t<e.length;t++)Mt(e[t].getAttribute(Y),jn(e[t].innerHTML),!0)},ht=(e,t,s,n,o,i,d)=>{if(s===n)return;let l=st(e,t),c=t.toLowerCase();if(r.vdomClass&&t==="class"){let a=e.classList,$=pt(s),u=pt(n);r.hydrateClientSide&&e["s-si"]&&d?(u.push(e["s-si"]),$.forEach(f=>{f.startsWith(e["s-si"])&&u.push(f)}),u=[...new Set(u)],a.add(...u)):(a.remove(...$.filter(f=>f&&!u.includes(f))),a.add(...u.filter(f=>f&&!$.includes(f))))}else if(r.vdomStyle&&t==="style"){if(r.updatable)for(let a in s)(!n||n[a]==null)&&(!r.hydrateServerSide&&a.includes("-")?e.style.removeProperty(a):e.style[a]="");for(let a in n)(!s||n[a]!==s[a])&&(!r.hydrateServerSide&&a.includes("-")?e.style.setProperty(a,n[a]):e.style[a]=n[a])}else if(!(r.vdomKey&&t==="key")){if(r.vdomRef&&t==="ref")n&&n(e);else if(r.vdomListener&&(r.lazyLoad?!l:!e.__lookupSetter__(t))&&t[0]==="o"&&t[1]==="n"){if(t[2]==="-"?t=t.slice(3):st(y,c)?t=c.slice(2):t=c[2]+t.slice(3),s||n){let a=t.endsWith(qt);t=t.replace(Hn,""),s&&m.rel(e,t,s,a),n&&m.ael(e,t,n,a)}}else if(r.vdomPropOrAttr){let a=Me(n);if((l||a&&n!==null)&&!o)try{if(e.tagName.includes("-"))e[t]!==n&&(e[t]=n);else{let u=n??"";t==="list"?l=!1:(s==null||e[t]!=u)&&(typeof e.__lookupSetter__(t)=="function"?e[t]=u:e.setAttribute(t,u))}}catch{}let $=!1;r.vdomXlink&&c!==(c=c.replace(/^xlink\:?/,""))&&(t=c,$=!0),n==null||n===!1?(n!==!1||e.getAttribute(t)==="")&&(r.vdomXlink&&$?e.removeAttributeNS(ot,t):e.removeAttribute(t)):(!l||i&4||o)&&!a&&e.nodeType===1&&(n=n===!0?"":n,r.vdomXlink&&$?e.setAttributeNS(ot,t,n):e.setAttribute(t,n))}}},Pn=/\s/,pt=e=>(typeof e=="object"&&e&&"baseVal"in e&&(e=e.baseVal),!e||typeof e!="string"?[]:e.split(Pn)),qt="Capture",Hn=new RegExp(qt+"$"),Pe=(e,t,s,n)=>{let o=t.$elm$.nodeType===11&&t.$elm$.host?t.$elm$.host:t.$elm$,i=e&&e.$attrs$||{},d=t.$attrs$||{};if(r.updatable)for(let l of vt(Object.keys(i)))l in d||ht(o,l,i[l],void 0,s,t.$flags$,n);for(let l of vt(Object.keys(d)))ht(o,l,i[l],d[l],s,t.$flags$,n)};function vt(e){return e.includes("ref")?[...e.filter(t=>t!=="ref"),"ref"]:e}var fe,ye,R,G=!1,Se=!1,Ie=!1,D=!1,_e=(e,t,s)=>{var n;let o=t.$children$[s],i=0,d,l,c;if(r.slotRelocation&&!G&&(Ie=!0,o.$tag$==="slot"&&(o.$flags$|=o.$children$?2:1)),r.isDev&&o.$elm$&&ue(`The JSX ${o.$text$!==null?`"${o.$text$}" text`:`"${o.$tag$}" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`),r.vdomText&&o.$text$!==null)d=o.$elm$=y.document.createTextNode(o.$text$);else if(r.slotRelocation&&o.$flags$&1)d=o.$elm$=r.isDebug||r.hydrateServerSide?Wn(o):y.document.createTextNode(""),r.vdomAttribute&&Pe(null,o,D);else{if(r.svg&&!D&&(D=o.$tag$==="svg"),!y.document)throw new Error("You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.");if(d=o.$elm$=r.svg?y.document.createElementNS(D?is:ls,!G&&r.slotRelocation&&o.$flags$&2?"slot-fb":o.$tag$):y.document.createElement(!G&&r.slotRelocation&&o.$flags$&2?"slot-fb":o.$tag$),r.svg&&D&&o.$tag$==="foreignObject"&&(D=!1),r.vdomAttribute&&Pe(null,o,D),(r.scoped||r.hydrateServerSide)&&bs(fe)&&d["s-si"]!==fe&&d.classList.add(d["s-si"]=fe),o.$children$)for(i=0;i<o.$children$.length;++i)l=_e(e,o,i),l&&d.appendChild(l);r.svg&&(o.$tag$==="svg"?D=!1:d.tagName==="foreignObject"&&(D=!0))}return d["s-hn"]=R,r.slotRelocation&&o.$flags$&3&&(d["s-sr"]=!0,d["s-cr"]=ye,d["s-sn"]=o.$name$||"",d["s-rf"]=(n=o.$attrs$)==null?void 0:n.ref,kt(d),c=e&&e.$children$&&e.$children$[s],c&&c.$tag$===o.$tag$&&e.$elm$&&(r.experimentalSlotFixes?Kt(e.$elm$):ee(e.$elm$,!1)),(r.scoped||r.hydrateServerSide)&&Jt(ye,d,t.$elm$,e?.$elm$)),d},Kt=e=>{m.$flags$|=1;let t=e.closest(R.toLowerCase());if(t!=null){let s=Array.from(t.__childNodes||t.childNodes).find(o=>o["s-cr"]),n=Array.from(e.__childNodes||e.childNodes);for(let o of s?n.reverse():n)o["s-sh"]!=null&&(k(t,o,s??null),o["s-sh"]=void 0,Ie=!0)}m.$flags$&=-2},ee=(e,t)=>{m.$flags$|=1;let s=Array.from(e.__childNodes||e.childNodes);if(e["s-sr"]&&r.experimentalSlotFixes){let n=e;for(;n=n.nextSibling;)n&&n["s-sn"]===e["s-sn"]&&n["s-sh"]===R&&s.push(n)}for(let n=s.length-1;n>=0;n--){let o=s[n];o["s-hn"]!==R&&o["s-ol"]&&(k(te(o).parentNode,o,te(o)),o["s-ol"].remove(),o["s-ol"]=void 0,o["s-sh"]=void 0,Ie=!0),t&&ee(o,t)}m.$flags$&=-2},Xt=(e,t,s,n,o,i)=>{let d=r.slotRelocation&&e["s-cr"]&&e["s-cr"].parentNode||e,l;for(r.shadowDom&&d.shadowRoot&&d.tagName===R&&(d=d.shadowRoot);o<=i;++o)n[o]&&(l=_e(null,s,o),l&&(n[o].$elm$=l,k(d,l,r.slotRelocation?te(t):t)))},Qt=(e,t,s)=>{for(let n=t;n<=s;++n){let o=e[n];if(o){let i=o.$elm$;Gt(o),i&&(r.slotRelocation&&(Se=!0,i["s-ol"]?i["s-ol"].remove():ee(i,!0)),i.remove())}}},Fn=(e,t,s,n,o=!1)=>{let i=0,d=0,l=0,c=0,a=t.length-1,$=t[0],u=t[a],f=n.length-1,p=n[0],h=n[f],v,g;for(;i<=a&&d<=f;)if($==null)$=t[++i];else if(u==null)u=t[--a];else if(p==null)p=n[++d];else if(h==null)h=n[--f];else if(ae($,p,o))W($,p,o),$=t[++i],p=n[++d];else if(ae(u,h,o))W(u,h,o),u=t[--a],h=n[--f];else if(ae($,h,o))r.slotRelocation&&($.$tag$==="slot"||h.$tag$==="slot")&&ee($.$elm$.parentNode,!1),W($,h,o),k(e,$.$elm$,u.$elm$.nextSibling),$=t[++i],h=n[--f];else if(ae(u,p,o))r.slotRelocation&&($.$tag$==="slot"||h.$tag$==="slot")&&ee(u.$elm$.parentNode,!1),W(u,p,o),k(e,u.$elm$,$.$elm$),u=t[--a],p=n[++d];else{if(l=-1,r.vdomKey){for(c=i;c<=a;++c)if(t[c]&&t[c].$key$!==null&&t[c].$key$===p.$key$){l=c;break}}r.vdomKey&&l>=0?(g=t[l],g.$tag$!==p.$tag$?v=_e(t&&t[d],s,l):(W(g,p,o),t[l]=void 0,v=g.$elm$),p=n[++d]):(v=_e(t&&t[d],s,d),p=n[++d]),v&&(r.slotRelocation?k(te($.$elm$).parentNode,v,te($.$elm$)):k($.$elm$.parentNode,v,$.$elm$))}i>a?Xt(e,n[f+1]==null?null:n[f+1].$elm$,s,n,d,f):r.updatable&&d>f&&Qt(t,i,a)},ae=(e,t,s=!1)=>e.$tag$===t.$tag$?r.slotRelocation&&e.$tag$==="slot"?e.$name$===t.$name$:r.vdomKey&&!s?e.$key$===t.$key$:(s&&!e.$key$&&t.$key$&&(e.$key$=t.$key$),!0):!1,te=e=>e&&e["s-ol"]||e,W=(e,t,s=!1)=>{let n=t.$elm$=e.$elm$,o=e.$children$,i=t.$children$,d=t.$tag$,l=t.$text$,c;!r.vdomText||l===null?(r.svg&&(D=d==="svg"?!0:d==="foreignObject"?!1:D),(r.vdomAttribute||r.reflect)&&(r.slot&&d==="slot"&&!G&&r.experimentalSlotFixes&&e.$name$!==t.$name$&&(t.$elm$["s-sn"]=t.$name$||"",Kt(t.$elm$.parentElement)),Pe(e,t,D,s)),r.updatable&&o!==null&&i!==null?Fn(n,o,t,i,s):i!==null?(r.updatable&&r.vdomText&&e.$text$!==null&&(n.textContent=""),Xt(n,null,t,i,0,i.length-1)):!s&&r.updatable&&o!==null&&Qt(o,0,o.length-1),r.svg&&D&&d==="svg"&&(D=!1)):r.vdomText&&r.slotRelocation&&(c=n["s-cr"])?c.parentNode.textContent=l:r.vdomText&&e.$text$!==l&&(n.data=l)},N=[],Yt=e=>{let t,s,n,o=e.__childNodes||e.childNodes;for(let i of o){if(i["s-sr"]&&(t=i["s-cr"])&&t.parentNode){s=t.parentNode.__childNodes||t.parentNode.childNodes;let d=i["s-sn"];for(n=s.length-1;n>=0;n--)if(t=s[n],!t["s-cn"]&&!t["s-nr"]&&t["s-hn"]!==i["s-hn"]&&(!r.experimentalSlotFixes||!t["s-sh"]||t["s-sh"]!==i["s-hn"]))if(at(t,d)){let l=N.find(c=>c.$nodeToRelocate$===t);Se=!0,t["s-sn"]=t["s-sn"]||d,l?(l.$nodeToRelocate$["s-sh"]=i["s-hn"],l.$slotRefNode$=i):(t["s-sh"]=i["s-hn"],N.push({$slotRefNode$:i,$nodeToRelocate$:t})),t["s-sr"]&&N.map(c=>{at(c.$nodeToRelocate$,t["s-sn"])&&(l=N.find(a=>a.$nodeToRelocate$===t),l&&!c.$slotRefNode$&&(c.$slotRefNode$=l.$slotRefNode$))})}else N.some(l=>l.$nodeToRelocate$===t)||N.push({$nodeToRelocate$:t})}i.nodeType===1&&Yt(i)}},Gt=e=>{r.vdomRef&&(e.$attrs$&&e.$attrs$.ref&&e.$attrs$.ref(null),e.$children$&&e.$children$.map(Gt))},k=(e,t,s)=>{if(r.scoped&&typeof t["s-sn"]=="string"&&t["s-sr"]&&t["s-cr"])Jt(t["s-cr"],t,e,t.parentElement);else if(r.experimentalSlotFixes&&typeof t["s-sn"]=="string"){e.getRootNode().nodeType!==11&&Bt(t),e.insertBefore(t,s);let{slotNode:n}=Ke(t);return n&&De(n),t}return r.experimentalSlotFixes&&e.__insertBefore?e.__insertBefore(t,s):e?.insertBefore(t,s)};function Jt(e,t,s,n){var o,i;let d;if(e&&typeof t["s-sn"]=="string"&&t["s-sr"]&&e.parentNode&&e.parentNode["s-sc"]&&(d=t["s-si"]||e.parentNode["s-sc"])){let l=t["s-sn"],c=t["s-hn"];if((o=s.classList)==null||o.add(d+"-s"),n&&((i=n.classList)!=null&&i.contains(d+"-s"))){let a=(n.__childNodes||n.childNodes)[0],$=!1;for(;a;){if(a["s-sn"]!==l&&a["s-hn"]===c&&a["s-sr"]){$=!0;break}a=a.nextSibling}$||n.classList.remove(d+"-s")}}}var gt=(e,t,s=!1)=>{var n,o,i,d,l;let c=e.$hostElement$,a=e.$cmpMeta$,$=e.$vnode$||X(null,null),f=Re(t)?t:Ut(null,null,t);if(R=c.tagName,r.isDev&&Array.isArray(t)&&t.some(Re))throw new Error(`The <Host> must be the single root component.
Looks like the render() function of "${R.toLowerCase()}" is returning an array that contains the <Host>.

The render() function should look like this instead:

render() {
  // Do not return an array
  return (
    <Host>{content}</Host>
  );
}
  `);if(r.reflect&&a.$attrsToReflect$&&(f.$attrs$=f.$attrs$||{},a.$attrsToReflect$.map(([p,h])=>f.$attrs$[h]=c[p])),s&&f.$attrs$)for(let p of Object.keys(f.$attrs$))c.hasAttribute(p)&&!["key","ref","style","class"].includes(p)&&(f.$attrs$[p]=c[p]);if(f.$tag$=null,f.$flags$|=4,e.$vnode$=f,f.$elm$=$.$elm$=r.shadowDom&&c.shadowRoot||c,(r.scoped||r.shadowDom)&&(fe=c["s-sc"]),G=K&&!!(a.$flags$&1)&&!(a.$flags$&128),r.slotRelocation&&(ye=c["s-cr"],Se=!1),W($,f,s),r.slotRelocation){if(m.$flags$|=1,Ie){Yt(f.$elm$);for(let p of N){let h=p.$nodeToRelocate$;if(!h["s-ol"]&&y.document){let v=r.isDebug||r.hydrateServerSide?Mn(h):y.document.createTextNode("");v["s-nr"]=h,k(h.parentNode,h["s-ol"]=v,h)}}for(let p of N){let h=p.$nodeToRelocate$,v=p.$slotRefNode$;if(v){let g=v.parentNode,L=v.nextSibling;if(!r.hydrateServerSide&&(!r.experimentalSlotFixes||L&&L.nodeType===1)){let A=(n=h["s-ol"])==null?void 0:n.previousSibling;for(;A;){let _=(o=A["s-nr"])!=null?o:null;if(_&&_["s-sn"]===h["s-sn"]&&g===(_.__parentNode||_.parentNode)){for(_=_.nextSibling;_===h||_?.["s-sr"];)_=_?.nextSibling;if(!_||!_["s-nr"]){L=_;break}}A=A.previousSibling}}let H=h.__parentNode||h.parentNode,w=h.__nextSibling||h.nextSibling;(!L&&g!==H||w!==L)&&h!==L&&(!r.experimentalSlotFixes&&!h["s-hn"]&&h["s-ol"]&&(h["s-hn"]=h["s-ol"].parentNode.nodeName),k(g,h,L),h.nodeType===1&&h.tagName!=="SLOT-FB"&&(h.hidden=(i=h["s-ih"])!=null?i:!1)),h&&typeof v["s-rf"]=="function"&&v["s-rf"](v)}else h.nodeType===1&&(s&&(h["s-ih"]=(d=h.hidden)!=null?d:!1),h.hidden=!0)}}Se&&Le(f.$elm$),m.$flags$&=-2,N.length=0}if(r.experimentalScopedSlotChanges&&a.$flags$&2){let p=f.$elm$.__childNodes||f.$elm$.childNodes;for(let h of p)h["s-hn"]!==R&&!h["s-sh"]&&(s&&h["s-ih"]==null&&(h["s-ih"]=(l=h.hidden)!=null?l:!1),h.hidden=!0)}ye=void 0},Wn=e=>{var t;return(t=y.document)==null?void 0:t.createComment(`<slot${e.$name$?' name="'+e.$name$+'"':""}> (host=${R.toLowerCase()})`)},Mn=e=>{var t;return(t=y.document)==null?void 0:t.createComment("org-location for "+(e.localName?`<${e.localName}> (host=${e["s-hn"]})`:`[${e.textContent}]`))},Zt=(e,t)=>{if(r.asyncLoading&&t&&!e.$onRenderResolve$&&t["s-p"]){let s=t["s-p"].push(new Promise(n=>e.$onRenderResolve$=()=>{t["s-p"].splice(s-1,1),n()}))}},Je=(e,t)=>{if(r.taskQueue&&r.updatable&&(e.$flags$|=16),r.asyncLoading&&e.$flags$&4){e.$flags$|=512;return}Zt(e,e.$ancestorComponent$);let s=()=>qn(e,t);return r.taskQueue?_s(s):s()},qn=(e,t)=>{let s=e.$hostElement$,n=B("scheduleUpdate",e.$cmpMeta$.$tagName$),o=r.lazyLoad?e.$lazyInstance$:s;if(!o)throw new Error(`Can't render component <${s.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \`externalRuntime: true\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);let i;return t?(r.lazyLoad&&r.hostListener&&(e.$flags$|=256,e.$queuedListeners$&&(e.$queuedListeners$.map(([d,l])=>O(o,d,l,s)),e.$queuedListeners$=void 0)),q(s,"componentWillLoad"),i=O(o,"componentWillLoad",void 0,s)):(q(s,"componentWillUpdate"),i=O(o,"componentWillUpdate",void 0,s)),q(s,"componentWillRender"),i=mt(i,()=>O(o,"componentWillRender",void 0,s)),n(),mt(i,()=>Xn(e,o,t))},mt=(e,t)=>Kn(e)?e.then(t).catch(s=>{console.error(s),t()}):t(),Kn=e=>e instanceof Promise||e&&e.then&&typeof e.then=="function",Xn=(e,t,s)=>ie(null,null,function*(){var n;let o=e.$hostElement$,i=B("update",e.$cmpMeta$.$tagName$),d=o["s-rc"];r.style&&s&&Un(e);let l=B("render",e.$cmpMeta$.$tagName$);if(r.isDev&&(e.$flags$|=1024),r.hydrateServerSide?yield St(e,t,o,s):St(e,t,o,s),r.isDev&&(e.$renderCount$=e.$renderCount$===void 0?1:e.$renderCount$+1,e.$flags$&=-1025),r.hydrateServerSide)try{Vt(o),s&&(e.$cmpMeta$.$flags$&1?o["s-en"]="":e.$cmpMeta$.$flags$&2&&(o["s-en"]="c"))}catch(c){U(c,o)}if(r.asyncLoading&&d&&(d.map(c=>c()),o["s-rc"]=void 0),l(),i(),r.asyncLoading){let c=(n=o["s-p"])!=null?n:[],a=()=>_t(e);c.length===0?a():(Promise.all(c).then(a),e.$flags$|=4,c.length=0)}else _t(e)}),yt=null,St=(e,t,s,n)=>{let o=!!r.allRenderFn,i=!!r.lazyLoad,d=!!r.taskQueue,l=!!r.updatable;try{if(yt=t,t=(o||t.render)&&t.render(),l&&d&&(e.$flags$&=-17),(l||i)&&(e.$flags$|=2),r.hasRenderFn||r.reflect)if(r.vdomRender||r.reflect){if(r.hydrateServerSide)return Promise.resolve(t).then(c=>gt(e,c,n));gt(e,t,n)}else{let c=s.shadowRoot;e.$cmpMeta$.$flags$&1?c.textContent=t:s.textContent=t}}catch(c){U(c,e.$hostElement$)}return yt=null,null};var _t=e=>{let t=e.$cmpMeta$.$tagName$,s=e.$hostElement$,n=B("postUpdate",t),o=r.lazyLoad?e.$lazyInstance$:s,i=e.$ancestorComponent$;r.isDev&&(e.$flags$|=1024),O(o,"componentDidRender",void 0,s),r.isDev&&(e.$flags$&=-1025),q(s,"componentDidRender"),e.$flags$&64?(r.isDev&&(e.$flags$|=1024),O(o,"componentDidUpdate",void 0,s),r.isDev&&(e.$flags$&=-1025),q(s,"componentDidUpdate"),n()):(e.$flags$|=64,r.asyncLoading&&r.cssAnnotations&&Yn(s),r.isDev&&(e.$flags$|=2048),O(o,"componentDidLoad",void 0,s),r.isDev&&(e.$flags$&=-2049),q(s,"componentDidLoad"),n(),r.asyncLoading&&(e.$onReadyResolve$(s),i||Qn(t))),r.method&&r.lazyLoad&&e.$onInstanceResolve$(s),r.asyncLoading&&(e.$onRenderResolve$&&(e.$onRenderResolve$(),e.$onRenderResolve$=void 0),e.$flags$&512&&be(()=>Je(e,!1)),e.$flags$&=-517)};var Qn=e=>{r.asyncQueue&&(m.$flags$|=2),be(()=>Ye(y,"appload",{detail:{namespace:le}})),r.profile&&performance.measure&&performance.measure(`[Stencil] ${le} initial load (by ${e})`,"st:app:start")},O=(e,t,s,n)=>{if(e&&e[t])try{return e[t](s)}catch(o){U(o,n)}},q=(e,t)=>{r.lifecycleDOMEvents&&Ye(e,"stencil_"+t,{bubbles:!0,composed:!0,detail:{namespace:le}})},Yn=e=>{var t,s;return r.hydratedClass?e.classList.add((t=r.hydratedSelectorName)!=null?t:"hydrated"):r.hydratedAttribute?e.setAttribute((s=r.hydratedSelectorName)!=null?s:"hydrated",""):void 0},Vt=e=>{let t=e.children;if(t!=null)for(let s=0,n=t.length;s<n;s++){let o=t[s];typeof o.connectedCallback=="function"&&o.connectedCallback(),Vt(o)}},bt=(e,t)=>E(e).$instanceValues$.get(t),ce=(e,t,s,n)=>{let o=E(e);if(r.lazyLoad&&!o)throw new Error(`Couldn't find host element for "${n.$tagName$}" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).`);let i=r.lazyLoad?o.$hostElement$:e,d=o.$instanceValues$.get(t),l=o.$flags$,c=r.lazyLoad?o.$lazyInstance$:i;s=me(s,n.$members$[t][0]);let a=Number.isNaN(d)&&Number.isNaN(s),$=s!==d&&!a;if((!r.lazyLoad||!(l&8)||d===void 0)&&$&&(o.$instanceValues$.set(t,s),r.isDev&&(o.$flags$&1024?J(`The state/prop "${t}" changed during rendering. This can potentially lead to infinite-loops and other bugs.`,`
Element`,i,`
New value`,s,`
Old value`,d):o.$flags$&2048&&J(`The state/prop "${t}" changed during "componentDidLoad()", this triggers extra re-renders, try to setup on "componentWillLoad()"`,`
Element`,i,`
New value`,s,`
Old value`,d)),!r.lazyLoad||c)){if(r.watchCallback&&n.$watchers$&&l&128){let u=n.$watchers$[t];u&&u.map(f=>{try{c[f](s,d,t)}catch(p){U(p,i)}})}if(r.updatable&&(l&18)===2){if(c.componentShouldUpdate&&c.componentShouldUpdate(s,d,t)===!1)return;Je(o,!1)}}},es=(e,t,s)=>{var n,o;let i=e.prototype;if(r.isTesting){if(i.__stencilAugmented)return;i.__stencilAugmented=!0}if(r.formAssociated&&t.$flags$&64&&s&1&&ms.forEach(d=>{let l=i[d];Object.defineProperty(i,d,{value(...c){let a=E(this),$=r.lazyLoad?a.$lazyInstance$:this;if(!$)a.$onReadyPromise$.then(u=>{let f=u[d];typeof f=="function"&&f.call(u,...c)});else{let u=r.lazyLoad?$[d]:l;typeof u=="function"&&u.call($,...c)}}})}),r.member&&t.$members$||r.watchCallback&&(t.$watchers$||e.watchers)){r.watchCallback&&e.watchers&&!t.$watchers$&&(t.$watchers$=e.watchers);let d=Object.entries((n=t.$members$)!=null?n:{});if(d.map(([l,[c]])=>{if((r.prop||r.state)&&(c&31||(!r.lazyLoad||s&2)&&c&32)){let{get:a,set:$}=Object.getOwnPropertyDescriptor(i,l)||{};a&&(t.$members$[l][0]|=2048),$&&(t.$members$[l][0]|=4096),(s&1||!a)&&Object.defineProperty(i,l,{get(){if(r.lazyLoad){if((t.$members$[l][0]&2048)===0)return bt(this,l);let u=E(this),f=u?u.$lazyInstance$:i;return f?f[l]:void 0}if(!r.lazyLoad)return a?a.apply(this):bt(this,l)},configurable:!0,enumerable:!0}),Object.defineProperty(i,l,{set(u){let f=E(this);if(r.isDev&&(s&1)===0&&(t.$members$[l][0]&4096)===0&&(f&&f.$flags$&8)===0&&(c&31)!==0&&(c&1024)===0&&J(`@Prop() "${l}" on <${t.$tagName$}> is immutable but was modified from within the component.
More information: https://stenciljs.com/docs/properties#prop-mutability`),$){let p=c&32?this[l]:f.$hostElement$[l];typeof p>"u"&&f.$instanceValues$.get(l)?u=f.$instanceValues$.get(l):!f.$instanceValues$.get(l)&&p&&f.$instanceValues$.set(l,p),$.apply(this,[me(u,c)]),u=c&32?this[l]:f.$hostElement$[l],ce(this,l,u,t);return}if(!r.lazyLoad){ce(this,l,u,t);return}if(r.lazyLoad){if((s&1)===0||(t.$members$[l][0]&4096)===0){ce(this,l,u,t),s&1&&!f.$lazyInstance$&&f.$onReadyPromise$.then(()=>{t.$members$[l][0]&4096&&f.$lazyInstance$[l]!==f.$instanceValues$.get(l)&&(f.$lazyInstance$[l]=u)});return}let p=()=>{let h=f.$lazyInstance$[l];!f.$instanceValues$.get(l)&&h&&f.$instanceValues$.set(l,h),f.$lazyInstance$[l]=me(u,c),ce(this,l,f.$lazyInstance$[l],t)};f.$lazyInstance$?p():f.$onReadyPromise$.then(()=>p())}}})}else r.lazyLoad&&r.method&&s&1&&c&64&&Object.defineProperty(i,l,{value(...a){var $;let u=E(this);return($=u?.$onInstancePromise$)==null?void 0:$.then(()=>{var f;return(f=u.$lazyInstance$)==null?void 0:f[l](...a)})}})}),r.observeAttribute&&(!r.lazyLoad||s&1)){let l=new Map;i.attributeChangedCallback=function(c,a,$){m.jmp(()=>{var u;let f=l.get(c);if(this.hasOwnProperty(f)&&r.lazyLoad)$=this[f],delete this[f];else{if(i.hasOwnProperty(f)&&typeof this[f]=="number"&&this[f]==$)return;if(f==null){let h=E(this),v=h?.$flags$;if(v&&!(v&8)&&v&128&&$!==a){let g=r.lazyLoad?h.$hostElement$:this,L=r.lazyLoad?h.$lazyInstance$:g,H=(u=t.$watchers$)==null?void 0:u[c];H?.forEach(w=>{L[w]!=null&&L[w].call(L,$,a,c)})}return}}let p=Object.getOwnPropertyDescriptor(i,f);$=$===null&&typeof this[f]=="boolean"?!1:$,$!==this[f]&&(!p.get||p.set)&&(this[f]=$)})},e.observedAttributes=Array.from(new Set([...Object.keys((o=t.$watchers$)!=null?o:{}),...d.filter(([c,a])=>a[0]&15).map(([c,a])=>{var $;let u=a[1]||c;return l.set(u,c),r.reflect&&a[0]&512&&(($=t.$attrsToReflect$)==null||$.push([c,u])),u})]))}}return e},Lt=(e,t,s,n)=>ie(null,null,function*(){let o;if((t.$flags$&32)===0){t.$flags$|=32;let l=s.$lazyBundleId$;if(r.lazyLoad&&l){let c=$s(s,t,n);if(c&&"then"in c){let $=Qs(`st:load:${s.$tagName$}:${t.$modeName$}`,`[Stencil] Load module for <${s.$tagName$}>`);o=yield c,$()}else o=c;if(!o)throw new Error(`Constructor for "${s.$tagName$}#${t.$modeName$}" was not found`);r.member&&!o.isProxied&&(r.watchCallback&&(s.$watchers$=o.watchers),es(o,s,2),o.isProxied=!0);let a=B("createInstance",s.$tagName$);r.member&&(t.$flags$|=8);try{new o(t)}catch($){U($,e)}r.member&&(t.$flags$&=-9),r.watchCallback&&(t.$flags$|=128),a(),He(t.$lazyInstance$,e)}else{o=e.constructor;let c=e.localName;customElements.whenDefined(c).then(()=>t.$flags$|=128)}if(r.style&&o&&o.style){let c;typeof o.style=="string"?c=o.style:r.mode&&typeof o.style!="string"&&(t.$modeName$=Rn(e),t.$modeName$&&(c=o.style[t.$modeName$]),r.hydrateServerSide&&t.$modeName$&&e.setAttribute("s-mode",t.$modeName$));let a=Ge(s,t.$modeName$);if(!he.has(a)){let $=B("registerStyles",s.$tagName$);r.hydrateServerSide&&r.shadowDom&&s.$flags$&128&&(c=On(c,a,!0)),Mt(a,c,!!(s.$flags$&1)),$()}}}let i=t.$ancestorComponent$,d=()=>Je(t,!0);r.asyncLoading&&i&&i["s-rc"]?i["s-rc"].push(d):d()}),He=(e,t)=>{r.lazyLoad&&O(e,"connectedCallback",void 0,t)},Gn=e=>{if((m.$flags$&1)===0){let t=E(e),s=t.$cmpMeta$,n=B("connectedCallback",s.$tagName$);if(r.hostListenerTargetParent&&Fe(e,t,s.$listeners$,!0),t.$flags$&1)Fe(e,t,s.$listeners$,!1),t?.$lazyInstance$?He(t.$lazyInstance$,e):t?.$onReadyPromise$&&t.$onReadyPromise$.then(()=>He(t.$lazyInstance$,e));else{t.$flags$|=1;let o;if(r.hydrateClientSide&&(o=e.getAttribute(Z),o)){if(r.shadowDom&&K&&s.$flags$&1){let i=r.mode?ze(e.shadowRoot,s,e.getAttribute("s-mode")):ze(e.shadowRoot,s);e.classList.remove(i+"-h",i+"-s")}else if(r.scoped&&s.$flags$&2){let i=Ge(s,r.mode?e.getAttribute("s-mode"):void 0);e["s-sc"]=i}Vs(e,s.$tagName$,o,t)}if(r.slotRelocation&&!o&&(r.hydrateServerSide||(r.slot||r.shadowDom)&&s.$flags$&12)&&Jn(e),r.asyncLoading){let i=e;for(;i=i.parentNode||i.host;)if(r.hydrateClientSide&&i.nodeType===1&&i.hasAttribute("s-id")&&i["s-p"]||i["s-p"]){Zt(t,t.$ancestorComponent$=i);break}}r.prop&&!r.hydrateServerSide&&s.$members$&&Object.entries(s.$members$).map(([i,[d]])=>{if(d&31&&e.hasOwnProperty(i)){let l=e[i];delete e[i],e[i]=l}}),r.initializeNextTick?be(()=>Lt(e,t,s)):Lt(e,t,s)}n()}},Jn=e=>{if(!y.document)return;let t=e["s-cr"]=y.document.createComment(r.isDebug?`content-ref (host=${e.localName})`:"");t["s-cn"]=!0,k(e,t,e.firstChild)},we=(e,t)=>{r.lazyLoad&&O(e,"disconnectedCallback",void 0,t||e)},Zn=e=>ie(null,null,function*(){if((m.$flags$&1)===0){let t=E(e);r.hostListener&&t.$rmListeners$&&(t.$rmListeners$.map(s=>s()),t.$rmListeners$=void 0),r.lazyLoad?t?.$lazyInstance$?we(t.$lazyInstance$,e):t?.$onReadyPromise$&&t.$onReadyPromise$.then(()=>we(t.$lazyInstance$,e)):we(e)}M.has(e)&&M.delete(e),e.shadowRoot&&M.has(e.shadowRoot)&&M.delete(e.shadowRoot)});var Mr=(e,t)=>{let s={$flags$:t[0],$tagName$:t[1]};r.member&&(s.$members$=t[2]),r.hostListener&&(s.$listeners$=t[3]),r.watchCallback&&(s.$watchers$=e.$watchers$),r.reflect&&(s.$attrsToReflect$=[]),r.shadowDom&&!K&&s.$flags$&1&&(s.$flags$|=8),r.experimentalSlotFixes?r.scoped&&s.$flags$&2&&ws(e.prototype):(r.slotChildNodesFix&&Rt(e.prototype),r.cloneNodeFix&&wt(e.prototype),r.appendChildSlotFix&&Nt(e.prototype),r.scopedSlotTextContentFix&&s.$flags$&2&&Ot(e.prototype)),r.hydrateClientSide&&r.shadowDom&&zn();let n=e.prototype.connectedCallback,o=e.prototype.disconnectedCallback;return Object.assign(e.prototype,{__hasHostListenerAttached:!1,__registerHost(){cs(this,s)},connectedCallback(){if(!this.__hasHostListenerAttached){let i=E(this);Fe(this,i,s.$listeners$,!1),this.__hasHostListenerAttached=!0}Gn(this),n&&n.call(this)},disconnectedCallback(){Zn(this),o&&o.call(this)},__attachShadow(){if(K){if(!this.shadowRoot)Ts.call(this,s);else if(this.shadowRoot.mode!=="open")throw new Error(`Unable to re-use existing shadow root for ${s.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`)}else this.shadowRoot=this}}),e.is=s.$tagName$,es(e,s,3)};var Fe=(e,t,s,n)=>{r.hostListener&&s&&y.document&&(r.hostListenerTargetParent&&(n?s=s.filter(([o])=>o&32):s=s.filter(([o])=>!(o&32))),s.map(([o,i,d])=>{let l=r.hostListenerTarget?er(y.document,e,o):e,c=Vn(t,d),a=tr(o);m.ael(l,i,c,a),(t.$rmListeners$=t.$rmListeners$||[]).push(()=>m.rel(l,i,c,a))}))},Vn=(e,t)=>s=>{var n;try{r.lazyLoad?e.$flags$&256?(n=e.$lazyInstance$)==null||n[t](s):(e.$queuedListeners$=e.$queuedListeners$||[]).push([t,s]):e.$hostElement$[t](s)}catch(o){U(o,e.$hostElement$)}},er=(e,t,s)=>r.hostListenerTargetDocument&&s&4?e:r.hostListenerTargetWindow&&s&8?y:r.hostListenerTargetBody&&s&16?e.body:r.hostListenerTargetParent&&s&32&&t.parentElement?t.parentElement:t,tr=e=>ys?{passive:(e&1)!==0,capture:(e&2)!==0}:(e&2)!==0;export{lr as a,hr as b,pr as c,_s as d,Ut as e,Ys as f,Tr as g,kr as h,jr as i,Mr as j};
