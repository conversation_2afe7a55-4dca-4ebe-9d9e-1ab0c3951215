import{f as r,g as i}from"./chunk-KRSA4YKC.js";import{b as s}from"./chunk-XFXTD7QR.js";import{j as a,k as c}from"./chunk-EHNA26RN.js";import{g as n}from"./chunk-2R6CW7ES.js";var h=()=>{let e=window;e.addEventListener("statusTap",()=>{a(()=>{let m=e.innerWidth,d=e.innerHeight,o=document.elementFromPoint(m/2,d/2);if(!o)return;let t=r(o);t&&new Promise(f=>s(t,f)).then(()=>{c(()=>n(null,null,function*(){t.style.setProperty("--overflow","hidden"),yield i(t,300),t.style.removeProperty("--overflow")}))})})})};export{h as startStatusTap};
