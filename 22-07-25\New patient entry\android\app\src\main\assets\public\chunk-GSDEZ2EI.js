import{a as at}from"./chunk-QUJFQN2Y.js";import{f as Rt,g as Lt}from"./chunk-642SEYX3.js";import{c as z,e as Ot,f as et,i as Yt,j as st,k as It}from"./chunk-KRSA4YKC.js";import{a as dt}from"./chunk-ILQCMFU7.js";import"./chunk-XCHF2ADM.js";import{h as At,i as Et,k as Tt,l as Ct,m as rt,p as Dt,q as Z,r as Pt,t as Bt,u as tt}from"./chunk-PK2JKDOR.js";import{a as kt}from"./chunk-3OLZKZOW.js";import{a as vt,b as xt,c as St}from"./chunk-B7F22SOJ.js";import{c as Mt}from"./chunk-BMJVVMK5.js";import{a as l}from"./chunk-753GXAUT.js";import{a as wt}from"./chunk-FPOZYJOD.js";import{c as yt,d as bt,h as S,i as G,m as j}from"./chunk-XFXTD7QR.js";import{a as it}from"./chunk-MTHZ7MWU.js";import"./chunk-WI5MSH4N.js";import"./chunk-JCOV4R6X.js";import{a as U}from"./chunk-CKP3SGE2.js";import{a as lt,b as nt,f as q,i as Nt,k as ct,m as H,n as _t,o as $t,p as Y}from"./chunk-EHNA26RN.js";import{g as D}from"./chunk-2R6CW7ES.js";var J=function(t){return t.Dark="DARK",t.Light="LIGHT",t.Default="DEFAULT",t}(J||{}),ft={getEngine(){let t=wt();if(t?.isPluginAvailable("StatusBar"))return t.Plugins.StatusBar},setStyle(t){let e=this.getEngine();e&&e.setStyle(t)},getStyle:function(){return D(this,null,function*(){let t=this.getEngine();if(!t)return J.Default;let{style:e}=yield t.getInfo();return e})}},pt=(t,e)=>{if(e===1)return 0;let n=1/(1-e),o=-(e*n);return t*n+o},Gt=()=>{!U||U.innerWidth>=768||ft.setStyle({style:J.Dark})},ht=(t=J.Default)=>{!U||U.innerWidth>=768||ft.setStyle({style:t})},zt=(t,e)=>D(null,null,function*(){typeof t.canDismiss!="function"||!(yield t.canDismiss(void 0,Z))||(e.isRunning()?e.onFinish(()=>{t.dismiss(void 0,"handler")},{oneTimeCallback:!0}):t.dismiss(void 0,"handler"))}),mt=t=>.00255275*2.71828**(-14.9619*t)-1.00255*2.71828**(-.0380968*t)+1,N={MIN_PRESENTING_SCALE:.915},Jt=(t,e,n,o)=>{let r=t.offsetHeight,a=!1,i=!1,d=null,f=null,b=.2,p=!0,w=0,u=()=>d&&z(d)?d.scrollY:!0,R=it({el:t,gestureName:"modalSwipeToClose",gesturePriority:Pt,direction:"y",threshold:10,canStart:A=>{let v=A.event.target;return v===null||!v.closest?!0:(d=et(v),d?(z(d)?f=S(d).querySelector(".inner-scroll"):f=d,!!!d.querySelector("ion-refresher")&&f.scrollTop===0):v.closest("ion-footer")===null)},onStart:A=>{let{deltaY:v}=A;p=u(),i=t.canDismiss!==void 0&&t.canDismiss!==!0,v>0&&d&&st(d),e.progressStart(!0,a?1:0)},onMove:A=>{let{deltaY:v}=A;v>0&&d&&st(d);let E=A.deltaY/r,C=E>=0&&i,L=C?b:.9999,V=C?mt(E/L):E,M=j(1e-4,V,L);e.progressStep(M),M>=.5&&w<.5?ht(n):M<.5&&w>=.5&&Gt(),w=M},onEnd:A=>{let v=A.velocityY,E=A.deltaY/r,C=E>=0&&i,L=C?b:.9999,V=C?mt(E/L):E,M=j(1e-4,V,L),_=(A.deltaY+v*1e3)/r,T=!C&&_>=.5,B=T?-.001:.001;T?(e.easing("cubic-bezier(0.32, 0.72, 0, 1)"),B+=at([0,0],[.32,.72],[0,1],[1,1],M)[0]):(e.easing("cubic-bezier(1, 0, 0.68, 0.28)"),B+=at([0,0],[1,0],[.68,.28],[1,1],M)[0]);let Q=qt(T?E*r:(1-M)*r,v);a=T,R.enable(!1),d&&It(d,p),e.onFinish(()=>{T||R.enable(!0)}).progressEnd(T?1:0,B,Q),C&&M>L/4?zt(t,e):T&&o()}});return R},qt=(t,e)=>j(400,t/Math.abs(e*1.1),500),Vt=t=>{let{currentBreakpoint:e,backdropBreakpoint:n,expandToScroll:o}=t,s=n===void 0||n<e,r=s?`calc(var(--backdrop-opacity) * ${e})`:"0",a=l("backdropAnimation").fromTo("opacity",0,r);s&&a.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]);let i=l("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-e*100}%)`}]),d=o?void 0:l("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:`${(1-e)*100}%`},{offset:1,opacity:1,maxHeight:`${e*100}%`}]);return{wrapperAnimation:i,backdropAnimation:a,contentAnimation:d}},Kt=t=>{let{currentBreakpoint:e,backdropBreakpoint:n}=t,o=`calc(var(--backdrop-opacity) * ${pt(e,n)})`,s=[{offset:0,opacity:o},{offset:1,opacity:0}],r=[{offset:0,opacity:o},{offset:n,opacity:0},{offset:1,opacity:0}],a=l("backdropAnimation").keyframes(n!==0?r:s);return{wrapperAnimation:l("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-e*100}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:a}},Qt=()=>{let t=l().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=l().fromTo("transform","translateY(100vh)","translateY(0vh)");return{backdropAnimation:t,wrapperAnimation:e,contentAnimation:void 0}},Ht=(t,e)=>{let{presentingEl:n,currentBreakpoint:o,expandToScroll:s}=e,r=S(t),{wrapperAnimation:a,backdropAnimation:i,contentAnimation:d}=o!==void 0?Vt(e):Qt();i.addElement(r.querySelector("ion-backdrop")),a.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!s&&d?.addElement(t.querySelector(".ion-page"));let f=l("entering-base").addElement(t).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([a]);if(d&&f.addAnimation(d),n){let b=window.innerWidth<768,p=n.tagName==="ION-MODAL"&&n.presentingElement!==void 0,w=S(n),u=l().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),k=document.body;if(b){let P=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",m=p?"-10px":P,I=N.MIN_PRESENTING_SCALE,R=`translateY(${m}) scale(${I})`;u.afterStyles({transform:R}).beforeAddWrite(()=>k.style.setProperty("background-color","black")).addElement(n).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:R,borderRadius:"10px 10px 0 0"}]),f.addAnimation(u)}else if(f.addAnimation(i),!p)a.fromTo("opacity","0","1");else{let m=`translateY(-10px) scale(${p?N.MIN_PRESENTING_SCALE:1})`;u.afterStyles({transform:m}).addElement(w.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:m}]);let I=l().afterStyles({transform:m}).addElement(w.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:m}]);f.addAnimation([u,I])}}else f.addAnimation(i);return f},Xt=()=>{let t=l().fromTo("opacity","var(--backdrop-opacity)",0),e=l().fromTo("transform","translateY(0vh)","translateY(100vh)");return{backdropAnimation:t,wrapperAnimation:e}},Wt=(t,e,n=500)=>{let{presentingEl:o,currentBreakpoint:s}=e,r=S(t),{wrapperAnimation:a,backdropAnimation:i}=s!==void 0?Kt(e):Xt();i.addElement(r.querySelector("ion-backdrop")),a.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});let d=l("leaving-base").addElement(t).easing("cubic-bezier(0.32,0.72,0,1)").duration(n).addAnimation(a);if(o){let f=window.innerWidth<768,b=o.tagName==="ION-MODAL"&&o.presentingElement!==void 0,p=S(o),w=l().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(k=>{if(k!==1)return;o.style.setProperty("overflow",""),Array.from(u.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(m=>m.presentingElement!==void 0).length<=1&&u.style.setProperty("background-color","")}),u=document.body;if(f){let k=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",P=b?"-10px":k,m=N.MIN_PRESENTING_SCALE,I=`translateY(${P}) scale(${m})`;w.addElement(o).keyframes([{offset:0,filter:"contrast(0.85)",transform:I,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),d.addAnimation(w)}else if(d.addAnimation(i),!b)a.fromTo("opacity","1","0");else{let P=`translateY(-10px) scale(${b?N.MIN_PRESENTING_SCALE:1})`;w.addElement(p.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:P},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);let m=l().addElement(p.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:P},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);d.addAnimation([w,m])}}else d.addAnimation(i);return d},te=(t,e,n=300)=>{let{presentingEl:o}=e;if(!o)return l("portrait-to-landscape-transition");let s=o.tagName==="ION-MODAL"&&o.presentingElement!==void 0,r=S(o),a=document.body,i=l("portrait-to-landscape-transition").addElement(t).easing("cubic-bezier(0.32,0.72,0,1)").duration(n),d=l().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"});if(s){let b=`translateY(-10px) scale(${N.MIN_PRESENTING_SCALE})`,p="translateY(0px) scale(1)";d.addElement(o).afterStyles({transform:p}).fromTo("transform",b,p).fromTo("filter","contrast(0.85)","contrast(1)");let w=l().addElement(r.querySelector(".modal-shadow")).afterStyles({transform:p,opacity:"0"}).fromTo("transform",b,p);i.addAnimation([d,w])}else{let f=S(t),b=l().addElement(f.querySelectorAll(".modal-wrapper, .modal-shadow")).fromTo("opacity","1","1"),p=l().addElement(f.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)","var(--backdrop-opacity)"),w=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",u=N.MIN_PRESENTING_SCALE,k=`translateY(${w}) scale(${u})`;d.addElement(o).afterStyles({transform:"translateY(0px) scale(1)","border-radius":"0px"}).beforeAddWrite(()=>a.style.setProperty("background-color","")).fromTo("transform",k,"translateY(0px) scale(1)").fromTo("filter","contrast(0.85)","contrast(1)").fromTo("border-radius","10px 10px 0 0","0px"),i.addAnimation([d,b,p])}return i},ee=(t,e,n=300)=>{let{presentingEl:o}=e;if(!o)return l("landscape-to-portrait-transition");let s=o.tagName==="ION-MODAL"&&o.presentingElement!==void 0,r=S(o),a=document.body,i=l("landscape-to-portrait-transition").addElement(t).easing("cubic-bezier(0.32,0.72,0,1)").duration(n),d=l().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"});if(s){let b=`translateY(-10px) scale(${N.MIN_PRESENTING_SCALE})`,p="translateY(0) scale(1)";d.addElement(o).afterStyles({transform:p}).fromTo("transform",b,p);let w=l().addElement(r.querySelector(".modal-shadow")).afterStyles({transform:p,opacity:"0"}).fromTo("transform",b,p);i.addAnimation([d,w])}else{let f=S(t),b=l().addElement(f.querySelectorAll(".modal-wrapper, .modal-shadow")).fromTo("opacity","1","1"),p=l().addElement(f.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)","var(--backdrop-opacity)"),w=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",u=N.MIN_PRESENTING_SCALE,k=`translateY(${w}) scale(${u})`;d.addElement(o).afterStyles({transform:k}).beforeAddWrite(()=>a.style.setProperty("background-color","black")).keyframes([{offset:0,transform:"translateY(0px) scale(1)",filter:"contrast(1)",borderRadius:"0px"},{offset:.2,transform:"translateY(0px) scale(1)",filter:"contrast(1)",borderRadius:"10px 10px 0 0"},{offset:1,transform:k,filter:"contrast(0.85)",borderRadius:"10px 10px 0 0"}]),i.addAnimation([d,b,p])}return i},ne=()=>{let t=l().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=l().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]);return{backdropAnimation:t,wrapperAnimation:e,contentAnimation:void 0}},oe=(t,e)=>{let{currentBreakpoint:n,expandToScroll:o}=e,s=S(t),{wrapperAnimation:r,backdropAnimation:a,contentAnimation:i}=n!==void 0?Vt(e):ne();a.addElement(s.querySelector("ion-backdrop")),r.addElement(s.querySelector(".modal-wrapper")),!o&&i?.addElement(t.querySelector(".ion-page"));let d=l().addElement(t).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([a,r]);return i&&d.addAnimation(i),d},ie=()=>{let t=l().fromTo("opacity","var(--backdrop-opacity)",0),e=l().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}]);return{backdropAnimation:t,wrapperAnimation:e}},re=(t,e)=>{let{currentBreakpoint:n}=e,o=S(t),{wrapperAnimation:s,backdropAnimation:r}=n!==void 0?Kt(e):ie();return r.addElement(o.querySelector("ion-backdrop")),s.addElement(o.querySelector(".modal-wrapper")),l().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([r,s])},se=(t,e,n,o,s,r,a=[],i,d,f,b)=>{let p=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],w=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-s,opacity:0},{offset:1,opacity:0}],u={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:s!==0?w:p,CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},k=t.querySelector("ion-content"),P=n.clientHeight,m=o,I=0,R=!1,A=null,v=null,E=null,C=null,L=.95,V=a[a.length-1],M=a[0],_=r.childAnimations.find(g=>g.id==="wrapperAnimation"),T=r.childAnimations.find(g=>g.id==="backdropAnimation"),B=r.childAnimations.find(g=>g.id==="contentAnimation"),Q=()=>{t.style.setProperty("pointer-events","auto"),e.style.setProperty("pointer-events","auto"),t.classList.remove(tt)},ut=()=>{t.style.setProperty("pointer-events","none"),e.style.setProperty("pointer-events","none"),t.classList.add(tt)},W=g=>{if(!v&&(v=Array.from(t.querySelectorAll("ion-footer")),!v.length))return;let c=t.querySelector(".ion-page");if(C=g,g==="stationary")v.forEach(y=>{y.classList.remove("modal-footer-moving"),y.style.removeProperty("position"),y.style.removeProperty("width"),y.style.removeProperty("height"),y.style.removeProperty("top"),y.style.removeProperty("left"),c?.style.removeProperty("padding-bottom"),c?.appendChild(y)});else{let y=0;v.forEach((h,$)=>{let O=h.getBoundingClientRect(),x=document.body.getBoundingClientRect();y+=h.clientHeight;let K=O.top-x.top,F=O.left-x.left;if(h.style.setProperty("--pinned-width",`${h.clientWidth}px`),h.style.setProperty("--pinned-height",`${h.clientHeight}px`),h.style.setProperty("--pinned-top",`${K}px`),h.style.setProperty("--pinned-left",`${F}px`),$===0){E=K;let ot=t.querySelector("ion-header");ot&&(E-=ot.clientHeight)}}),v.forEach(h=>{c?.style.setProperty("padding-bottom",`${y}px`),h.classList.add("modal-footer-moving"),h.style.setProperty("position","absolute"),h.style.setProperty("width","var(--pinned-width)"),h.style.setProperty("height","var(--pinned-height)"),h.style.setProperty("top","var(--pinned-top)"),h.style.setProperty("left","var(--pinned-left)"),document.body.appendChild(h)})}};_&&T&&(_.keyframes([...u.WRAPPER_KEYFRAMES]),T.keyframes([...u.BACKDROP_KEYFRAMES]),B?.keyframes([...u.CONTENT_KEYFRAMES]),r.progressStart(!0,1-m),m>s?Q():ut()),k&&m!==V&&i&&(k.scrollY=!1);let Ft=g=>{let c=et(g.event.target);if(m=d(),!i&&c)return(z(c)?S(c).querySelector(".inner-scroll"):c).scrollTop===0;if(m===1&&c){let y=z(c)?S(c).querySelector(".inner-scroll"):c;return!!!c.querySelector("ion-refresher")&&y.scrollTop===0}return!0},jt=g=>{if(R=t.canDismiss!==void 0&&t.canDismiss!==!0&&M===0,!i){let c=et(g.event.target);A=c&&z(c)?S(c).querySelector(".inner-scroll"):c}i||W("moving"),g.deltaY>0&&k&&(k.scrollY=!1),G(()=>{t.focus()}),r.progressStart(!0,1-m)},Ut=g=>{if(!i&&E!==null&&C!==null&&(g.currentY>=E&&C==="moving"?W("stationary"):g.currentY<E&&C==="stationary"&&W("moving")),!i&&g.deltaY<=0&&A)return;g.deltaY>0&&k&&(k.scrollY=!1);let c=1-m,y=a.length>1?1-a[1]:void 0,h=c+g.deltaY/P,$=y!==void 0&&h>=y&&R,O=$?L:.9999,x=$&&y!==void 0?y+mt((h-y)/(O-y)):h;I=j(1e-4,x,O),r.progressStep(I)},Zt=g=>{if(!i&&g.deltaY<=0&&A&&A.scrollTop>0){W("stationary");return}let c=g.velocityY,y=(g.deltaY+c*350)/P,h=m-y,$=a.reduce((O,x)=>Math.abs(x-h)<Math.abs(O-h)?x:O);gt({breakpoint:$,breakpointOffset:I,canDismiss:R,animated:!0})},gt=g=>{let{breakpoint:c,canDismiss:y,breakpointOffset:h,animated:$}=g,O=y&&c===0,x=O?m:c,K=x!==0;return m=0,_&&T&&(_.keyframes([{offset:0,transform:`translateY(${h*100}%)`},{offset:1,transform:`translateY(${(1-x)*100}%)`}]),T.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${pt(1-h,s)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${pt(x,s)})`}]),B&&B.keyframes([{offset:0,maxHeight:`${(1-h)*100}%`},{offset:1,maxHeight:`${x*100}%`}]),r.progressStep(0)),X.enable(!1),O?zt(t,r):K||f(),k&&(x===a[a.length-1]||!i)&&(k.scrollY=!0),!i&&x===0&&W("stationary"),new Promise(F=>{r.onFinish(()=>{K?(i||W("stationary"),_&&T?G(()=>{_.keyframes([...u.WRAPPER_KEYFRAMES]),T.keyframes([...u.BACKDROP_KEYFRAMES]),B?.keyframes([...u.CONTENT_KEYFRAMES]),r.progressStart(!0,1-x),m=x,b(m),m>s?Q():ut(),X.enable(!0),F()}):(X.enable(!0),F())):F()},{oneTimeCallback:!0}).progressEnd(1,0,$?500:0)})},X=it({el:n,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:Ft,onStart:jt,onMove:Ut,onEnd:Zt});return{gesture:X,moveSheetToBreakpoint:gt}},ae=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}',de=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}',le=class{constructor(t){Nt(this,t),this.didPresent=Y(this,"ionModalDidPresent",7),this.willPresent=Y(this,"ionModalWillPresent",7),this.willDismiss=Y(this,"ionModalWillDismiss",7),this.didDismiss=Y(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=Y(this,"ionBreakpointDidChange",7),this.didPresentShorthand=Y(this,"didPresent",7),this.willPresentShorthand=Y(this,"willPresent",7),this.willDismissShorthand=Y(this,"willDismiss",7),this.didDismissShorthand=Y(this,"didDismiss",7),this.ionMount=Y(this,"ionMount",7),this.lockController=kt(),this.triggerController=Bt(),this.coreDelegate=St(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.expandToScroll=!0,this.backdropBreakpoint=0,this.handleBehavior="none",this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.isOpen=!1,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0,this.onHandleClick=()=>{let{sheetTransition:e,handleBehavior:n}=this;n!=="cycle"||e!==void 0||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{let{sheetTransition:e}=this;e===void 0&&this.dismiss(void 0,Dt)},this.onLifecycle=e=>{let n=this.usersElement,o=ce[e.type];if(n&&o){let s=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:e.detail});n.dispatchEvent(s)}},this.onModalFocus=e=>{let{dragHandleEl:n,el:o}=this;e.target===o&&n&&n.tabIndex!==-1&&n.focus()},this.onSlotChange=({target:e})=>{e.assignedElements().forEach(o=>{o.querySelectorAll("ion-modal").forEach(s=>{s.getAttribute("data-parent-ion-modal")===null&&s.setAttribute("data-parent-ion-modal",this.el.id)})})}}onIsOpenChange(t,e){t===!0&&e===!1?this.present():t===!1&&e===!0&&this.dismiss()}triggerChanged(){let{trigger:t,el:e,triggerController:n}=this;t&&n.addClickListener(e,t)}onWindowResize(){q(this)!=="ios"||!this.presentingElement||this.enterAnimation||this.leaveAnimation||(clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this.handleViewTransition()},50))}breakpointsChanged(t){t!==void 0&&(this.sortedBreakpoints=t.sort((e,n)=>e-n))}connectedCallback(){let{el:t}=this;At(t),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener(),this.cleanupViewTransitionListener(),this.cleanupParentRemovalObserver()}componentWillLoad(){var t;let{breakpoints:e,initialBreakpoint:n,el:o,htmlAttributes:s}=this,r=this.isSheetModal=e!==void 0&&n!==void 0,a=["aria-label","role"];this.inheritedAttributes=bt(o,a),o.parentNode&&(this.cachedOriginalParent=o.parentNode),s!==void 0&&a.forEach(i=>{s[i]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[i]:s[i]}),delete s[i])}),r&&(this.currentBreakpoint=this.initialBreakpoint),e!==void 0&&n!==void 0&&!e.includes(n)&&nt("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),!((t=this.htmlAttributes)===null||t===void 0)&&t.id||Et(this.el)}componentDidLoad(){this.isOpen===!0&&G(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(t=!1){if(this.workingDelegate&&!t)return{delegate:this.workingDelegate,inline:this.inline};let e=this.el.parentNode,n=this.inline=e!==null&&!this.hasController,o=this.workingDelegate=n?this.delegate||this.coreDelegate:this.delegate;return{inline:n,delegate:o}}checkCanDismiss(t,e){return D(this,null,function*(){let{canDismiss:n}=this;return typeof n=="function"?n(t,e):n})}present(){return D(this,null,function*(){let t=yield this.lockController.lock();if(this.presented){t();return}let{presentingElement:e,el:n}=this;this.currentBreakpoint=this.initialBreakpoint;let{inline:o,delegate:s}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield vt(s,n,this.component,["ion-page"],this.componentProps,o),yt(n)?yield Lt(this.usersElement):this.keepContentsMounted||(yield Rt()),ct(()=>this.el.classList.add("show-modal"));let r=e!==void 0;r&&q(this)==="ios"&&(this.statusBarStyle=yield ft.getStyle(),Gt()),yield Tt(this,"modalEnter",Ht,oe,{presentingEl:e,currentBreakpoint:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll}),typeof window<"u"&&(this.keyboardOpenCallback=()=>{this.gesture&&(this.gesture.enable(!1),G(()=>{this.gesture&&this.gesture.enable(!0)}))},window.addEventListener(dt,this.keyboardOpenCallback)),this.isSheetModal?this.initSheetGesture():r&&this.initSwipeToClose(),this.initViewTransitionListener(),this.initParentRemovalObserver(),t()})}initSwipeToClose(){var t;if(q(this)!=="ios")return;let{el:e}=this,n=this.leaveAnimation||lt.get("modalLeave",Wt),o=this.animation=n(e,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!Ot(e)){Yt(e);return}let r=(t=this.statusBarStyle)!==null&&t!==void 0?t:J.Default;this.gesture=Jt(e,o,r,()=>{this.gestureAnimationDismissing=!0,ht(this.statusBarStyle),this.animation.onFinish(()=>D(this,null,function*(){yield this.dismiss(void 0,Z),this.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){let{wrapperEl:t,initialBreakpoint:e,backdropBreakpoint:n}=this;if(!t||e===void 0)return;let o=this.enterAnimation||lt.get("modalEnter",Ht),s=this.animation=o(this.el,{presentingEl:this.presentingElement,currentBreakpoint:e,backdropBreakpoint:n,expandToScroll:this.expandToScroll});s.progressStart(!0,1);let{gesture:r,moveSheetToBreakpoint:a}=se(this.el,this.backdropEl,t,e,n,s,this.sortedBreakpoints,this.expandToScroll,()=>{var i;return(i=this.currentBreakpoint)!==null&&i!==void 0?i:0},()=>this.sheetOnDismiss(),i=>{this.currentBreakpoint!==i&&(this.currentBreakpoint=i,this.ionBreakpointDidChange.emit({breakpoint:i}))});this.gesture=r,this.moveSheetToBreakpoint=a,this.gesture.enable(!0)}sheetOnDismiss(){this.gestureAnimationDismissing=!0,this.animation.onFinish(()=>D(this,null,function*(){this.currentBreakpoint=0,this.ionBreakpointDidChange.emit({breakpoint:this.currentBreakpoint}),yield this.dismiss(void 0,Z),this.gestureAnimationDismissing=!1}))}dismiss(t,e){return D(this,null,function*(){var n;if(this.gestureAnimationDismissing&&e!==Z)return!1;let o=yield this.lockController.lock();if(yield this.dismissNestedModals(),e!=="handler"&&!(yield this.checkCanDismiss(t,e)))return o(),!1;let{presentingElement:s}=this;s!==void 0&&q(this)==="ios"&&ht(this.statusBarStyle),typeof window<"u"&&this.keyboardOpenCallback&&(window.removeEventListener(dt,this.keyboardOpenCallback),this.keyboardOpenCallback=void 0);let a=yield Ct(this,t,e,"modalLeave",Wt,re,{presentingEl:s,currentBreakpoint:(n=this.currentBreakpoint)!==null&&n!==void 0?n:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll});if(a){let{delegate:i}=this.getDelegate();yield xt(i,this.usersElement),ct(()=>this.el.classList.remove("show-modal")),this.animation&&this.animation.destroy(),this.gesture&&this.gesture.destroy(),this.cleanupViewTransitionListener(),this.cleanupParentRemovalObserver()}return this.currentBreakpoint=void 0,this.animation=void 0,o(),a})}onDidDismiss(){return rt(this.el,"ionModalDidDismiss")}onWillDismiss(){return rt(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(t){return D(this,null,function*(){if(!this.isSheetModal){nt("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");return}if(!this.breakpoints.includes(t)){nt(`[ion-modal] - Attempted to set invalid breakpoint value ${t}. Please double check that the breakpoint value is part of your defined breakpoints.`);return}let{currentBreakpoint:e,moveSheetToBreakpoint:n,canDismiss:o,breakpoints:s,animated:r}=this;e!==t&&n&&(this.sheetTransition=n({breakpoint:t,breakpointOffset:1-e,canDismiss:o!==void 0&&o!==!0&&s[0]===0,animated:r}),yield this.sheetTransition,this.sheetTransition=void 0)})}getCurrentBreakpoint(){return D(this,null,function*(){return this.currentBreakpoint})}moveToNextBreakpoint(){return D(this,null,function*(){let{breakpoints:t,currentBreakpoint:e}=this;if(!t||e==null)return!1;let n=t.filter(a=>a!==0),s=(n.indexOf(e)+1)%n.length,r=n[s];return yield this.setCurrentBreakpoint(r),!0})}initViewTransitionListener(){q(this)!=="ios"||!this.presentingElement||this.enterAnimation||this.leaveAnimation||(this.currentViewIsPortrait=window.innerWidth<768)}handleViewTransition(){let t=window.innerWidth<768;if(this.currentViewIsPortrait===t)return;this.viewTransitionAnimation&&(this.viewTransitionAnimation.destroy(),this.viewTransitionAnimation=void 0);let{presentingElement:e}=this;if(!e)return;let n;this.currentViewIsPortrait&&!t?n=te(this.el,{presentingEl:e}):n=ee(this.el,{presentingEl:e}),this.currentViewIsPortrait=t,this.viewTransitionAnimation=n,n.play().then(()=>{this.viewTransitionAnimation=void 0,this.reinitSwipeToClose()})}cleanupViewTransitionListener(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=void 0),this.viewTransitionAnimation&&(this.viewTransitionAnimation.destroy(),this.viewTransitionAnimation=void 0)}reinitSwipeToClose(){q(this)!=="ios"||!this.presentingElement||(this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.animation&&(this.animation.progressEnd(0,0,0),this.animation.destroy(),this.animation=void 0),G(()=>{this.ensureCorrectModalPosition(),this.initSwipeToClose()}))}ensureCorrectModalPosition(){let{el:t,presentingElement:e}=this,o=S(t).querySelector(".modal-wrapper");if(o&&(o.style.transform="translateY(0vh)",o.style.opacity="1"),e?.tagName==="ION-MODAL")if(window.innerWidth<768){let r=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",a=N.MIN_PRESENTING_SCALE;e.style.transform=`translateY(${r}) scale(${a})`}else e.style.transform="translateY(0px) scale(1)"}dismissNestedModals(){return D(this,null,function*(){let t=document.querySelectorAll(`ion-modal[data-parent-ion-modal="${this.el.id}"]`);t?.forEach(e=>D(this,null,function*(){yield e.dismiss(void 0,"parent-dismissed")}))})}initParentRemovalObserver(){typeof MutationObserver>"u"||typeof window>"u"||!this.cachedOriginalParent||this.cachedOriginalParent.nodeType===Node.DOCUMENT_NODE||this.cachedOriginalParent.nodeType===Node.DOCUMENT_FRAGMENT_NODE||(this.parentRemovalObserver=new MutationObserver(t=>{t.forEach(e=>{if(e.type==="childList"&&e.removedNodes.length>0){let n=Array.from(e.removedNodes).some(s=>{var r,a;let i=s===this.cachedOriginalParent,d=this.cachedOriginalParent?(a=(r=s).contains)===null||a===void 0?void 0:a.call(r,this.cachedOriginalParent):!1;return i||d}),o=this.cachedOriginalParent&&!this.cachedOriginalParent.isConnected;(n||o)&&(this.dismiss(void 0,"parent-removed"),this.cachedOriginalParent=void 0)}})}),this.parentRemovalObserver.observe(document.body,{childList:!0,subtree:!0}))}cleanupParentRemovalObserver(){var t;(t=this.parentRemovalObserver)===null||t===void 0||t.disconnect(),this.parentRemovalObserver=void 0}render(){let{handle:t,isSheetModal:e,presentingElement:n,htmlAttributes:o,handleBehavior:s,inheritedAttributes:r,focusTrap:a,expandToScroll:i}=this,d=t!==!1&&e,f=q(this),b=n!==void 0&&f==="ios",p=s==="cycle";return H(_t,Object.assign({key:"9e9a7bd591eb17a225a00b4fa2e379e94601d17f","no-router":!0,tabIndex:p&&(e&&d)?0:-1},o,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[f]:!0,"modal-default":!b&&!e,"modal-card":b,"modal-sheet":e,"modal-no-expand-scroll":e&&!i,"overlay-hidden":!0,[tt]:a===!1},Mt(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle,onFocus:this.onModalFocus}),H("ion-backdrop",{key:"e5eae2c14f830f75e308fcd7f4c10c86fac5b962",ref:u=>this.backdropEl=u,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),f==="ios"&&H("div",{key:"e268f9cd310c3cf4e051b5b92524ce4fb70d005e",class:"modal-shadow"}),H("div",Object.assign({key:"9c380f36c18144c153077b15744d1c3346bce63e",role:"dialog"},r,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:u=>this.wrapperEl=u}),d&&H("button",{key:"2d5ee6d5959d97309c306e8ce72eb0f2c19be144",class:"modal-handle",tabIndex:p?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:p?this.onHandleClick:void 0,part:"handle",ref:u=>this.dragHandleEl=u}),H("slot",{key:"5590434c35ea04c42fc006498bc189038e15a298",onSlotchange:this.onSlotChange})))}get el(){return $t(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},ce={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};le.style={ios:ae,md:de};export{le as ion_modal};
