# Universal Database Service Setup Guide

This guide will help you set up the Universal Database Service for cross-platform PouchDB support in your Ionic Angular application.

## 🎯 Overview

The Universal Database Service automatically detects your platform (Web vs Mobile) and selects the appropriate PouchDB adapter:
- **Web**: IndexedDB adapter for browser storage
- **Mobile**: SQLite adapter for native mobile storage

## 📦 Dependencies

All required dependencies have been installed:

```bash
npm install pouchdb-adapter-cordova-sqlite @capacitor-community/sqlite pouchdb-adapter-idb pouchdb-find pouchdb-replication
```

## 🔧 Configuration

### 1. Capacitor Configuration

Update your `capacitor.config.ts`:

```typescript
import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'your.app.id',
  appName: 'Your App Name',
  webDir: 'www',
  bundledWebRuntime: false,
  plugins: {
    CapacitorSQLite: {
      iosDatabaseLocation: 'default',
      iosIsEncryption: false,
      iosKeychainPrefix: 'your-app-name',
      iosBiometric: {
        biometricAuth: false,
        biometricTitle: "Biometric login for capacitor sqlite"
      },
      androidIsEncryption: false,
      androidBiometric: {
        biometricAuth: false,
        biometricTitle: "Biometric login for capacitor sqlite",
        biometricSubTitle: "Log in using your biometric"
      }
    }
  }
};

export default config;
```

### 2. Service Usage

Import and use the service in your components:

```typescript
import { Component } from '@angular/core';
import { PouchdbService } from '../services/pouchdb.service';
import { mPatientData } from './patient-entry';

@Component({
  selector: 'app-patient-entry',
  templateUrl: './patient-entry.page.html'
})
export class PatientEntryPage {
  
  constructor(private db: PouchdbService) {}

  async ngOnInit() {
    // Get platform info
    const platformInfo = this.db.getPlatformInfo();
    console.log('Running on:', platformInfo.platform, 'with adapter:', platformInfo.adapter);
    
    // Load all patients
    const patients = await this.db.getAllPatient();
    console.log('Loaded patients:', patients.length);
  }

  async addPatient(patient: mPatientData) {
    try {
      const result = await this.db.addPatient(patient);
      console.log('Patient added:', result.id);
    } catch (error) {
      console.error('Failed to add patient:', error);
    }
  }

  async updatePatient(patient: mPatientData) {
    try {
      const result = await this.db.updatePatient(patient);
      console.log('Patient updated:', result.id);
    } catch (error) {
      console.error('Failed to update patient:', error);
    }
  }

  async deletePatient(patient: mPatientData) {
    try {
      const result = await this.db.deletePatient(patient);
      console.log('Patient deleted:', result.id);
    } catch (error) {
      console.error('Failed to delete patient:', error);
    }
  }
}
```

## 🔄 Sync Configuration

Configure remote sync with CouchDB:

```typescript
// Configure remote sync
this.db.configure({
  remoteUrl: 'https://your-couchdb-server.com/your-database',
  syncOptions: {
    live: false,
    retry: true,
    continuous: false
  }
});

// One-time sync
const syncResult = await this.db.syncWithServer();
console.log('Sync result:', syncResult);

// Live sync
const liveSync = await this.db.startLiveSync();
liveSync.on('change', (change) => {
  console.log('Sync change:', change);
});
```

## 🧪 Testing

Use the DatabaseTestService to verify everything is working:

```typescript
import { DatabaseTestService } from '../services/database-test.service';

@Component({...})
export class TestPage {
  
  constructor(private testService: DatabaseTestService) {}

  async runTests() {
    try {
      await this.testService.runTests();
      console.log('All tests passed!');
    } catch (error) {
      console.error('Tests failed:', error);
    }
  }

  async runPerformanceTest() {
    await this.testService.performanceTest(100);
  }
}
```

## 📱 Platform-Specific Setup

### Web Platform
- Uses IndexedDB automatically
- No additional setup required
- Works in all modern browsers

### Mobile Platform (Android/iOS)
- Uses SQLite via Capacitor Community SQLite plugin
- Requires native build
- Automatic database creation and management

### Building for Mobile

```bash
# Build the app
ionic build

# Add platforms
npx cap add android
npx cap add ios

# Sync and copy files
npx cap sync

# Open in native IDE
npx cap open android
npx cap open ios
```

## 🔍 Available Methods

### CRUD Operations
- `getAllPatient()`: Get all patients
- `addPatient(patient)`: Add new patient
- `updatePatient(patient)`: Update existing patient
- `deletePatient(patient)`: Delete patient
- `getPatient(id)`: Get patient by ID

### Search Operations
- `findPatients(selector, options)`: Advanced search using PouchDB find

### Sync Operations
- `syncWithServer(remoteUrl?)`: One-time sync
- `startLiveSync(remoteUrl?)`: Start live sync

### Utility Methods
- `getPlatformInfo()`: Get current platform and adapter info
- `getDatabaseInfo()`: Get database statistics
- `configure(config)`: Configure database settings
- `closeDatabase()`: Close database connections
- `destroyDatabase()`: Delete entire database (use with caution!)

## 🚀 Features

✅ **Universal Platform Support**: Automatically detects and adapts to Web/Mobile  
✅ **Same API Everywhere**: Identical code works on all platforms  
✅ **Production Ready**: Comprehensive error handling and logging  
✅ **Live Sync**: Real-time synchronization with CouchDB  
✅ **Offline First**: Works offline, syncs when online  
✅ **TypeScript Support**: Full type safety and IntelliSense  
✅ **Performance Optimized**: Efficient database operations  
✅ **Backward Compatible**: Drop-in replacement for existing PouchdbService  

## 🔧 Troubleshooting

### Common Issues

1. **SQLite not working on mobile**: Ensure @capacitor-community/sqlite is properly installed and synced
2. **Sync errors**: Check remote CouchDB URL and authentication
3. **TypeScript errors**: Ensure all dependencies are properly installed

### Debug Mode

Enable detailed logging:

```typescript
// In your app.module.ts or main component
console.log('Platform Info:', this.db.getPlatformInfo());
```

## 📚 Additional Resources

- [PouchDB Documentation](https://pouchdb.com/guides/)
- [Capacitor SQLite Plugin](https://github.com/capacitor-community/sqlite)
- [CouchDB Documentation](https://docs.couchdb.org/)
- [Ionic Framework](https://ionicframework.com/docs)
