import{D as g,H as m,J as O,e as P,k as I}from"./chunk-GXFEW3CR.js";import{b as D}from"./chunk-BMJVVMK5.js";import{b as C,f}from"./chunk-XFXTD7QR.js";import{c as b,f as B,i as z,m as d,n as A,o as _}from"./chunk-EHNA26RN.js";import{g as u}from"./chunk-2R6CW7ES.js";var F=":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:7px;padding-bottom:7px}:host button.ion-activated{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}",H=":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}",q=(()=>{let x=class{constructor(s){z(this,s),this.datetimeEl=null,this.overlayEl=null,this.datetimePresentation="date-time",this.datetimeActive=!1,this.color="primary",this.disabled=!1,this.getParsedDateValues=e=>e==null?[]:Array.isArray(e)?e:[e],this.setDateTimeText=()=>{var e,a,n,t,o;let{datetimeEl:c,datetimePresentation:p}=this;if(!c)return;let{value:$,locale:r,formatOptions:i,hourCycle:L,preferWheel:N,multiple:j,titleSelectedDatesFormatter:v}=c,h=this.getParsedDateValues($),y=I(h.length>0?h:[O()]);if(!y)return;let l=y[0],k=P(r,L);switch(this.dateText=this.timeText=void 0,p){case"date-time":case"time-date":let w=m(r,l,(e=i?.date)!==null&&e!==void 0?e:{month:"short",day:"numeric",year:"numeric"}),T=g(r,l,k,i?.time);N?this.dateText=`${w} ${T}`:(this.dateText=w,this.timeText=T);break;case"date":if(j&&h.length!==1){let E=`${h.length} days`;if(v!==void 0)try{E=v(h)}catch(V){b("[ion-datetime-button] - Exception in provided `titleSelectedDatesFormatter`:",V)}this.dateText=E}else this.dateText=m(r,l,(a=i?.date)!==null&&a!==void 0?a:{month:"short",day:"numeric",year:"numeric"});break;case"time":this.timeText=g(r,l,k,i?.time);break;case"month-year":this.dateText=m(r,l,(n=i?.date)!==null&&n!==void 0?n:{month:"long",year:"numeric"});break;case"month":this.dateText=m(r,l,(t=i?.time)!==null&&t!==void 0?t:{month:"long"});break;case"year":this.dateText=m(r,l,(o=i?.time)!==null&&o!==void 0?o:{year:"numeric"});break}},this.waitForDatetimeChanges=()=>u(this,null,function*(){let{datetimeEl:e}=this;return e?new Promise(a=>{f(e,"ionRender",a,{once:!0})}):Promise.resolve()}),this.handleDateClick=e=>u(this,null,function*(){let{datetimeEl:a,datetimePresentation:n}=this;if(!a)return;let t=!1;switch(n){case"date-time":case"time-date":let o=a.presentation!=="date";!a.preferWheel&&o&&(a.presentation="date",t=!0);break}this.selectedButton="date",this.presentOverlay(e,t,this.dateTargetEl)}),this.handleTimeClick=e=>{let{datetimeEl:a,datetimePresentation:n}=this;if(!a)return;let t=!1;switch(n){case"date-time":case"time-date":a.presentation!=="time"&&(a.presentation="time",t=!0);break}this.selectedButton="time",this.presentOverlay(e,t,this.timeTargetEl)},this.presentOverlay=(e,a,n)=>u(this,null,function*(){let{overlayEl:t}=this;t&&(t.tagName==="ION-POPOVER"?(a&&(yield this.waitForDatetimeChanges()),t.present(Object.assign(Object.assign({},e),{detail:{ionShadowTarget:n}}))):t.present())})}componentWillLoad(){return u(this,null,function*(){let{datetime:s}=this;if(!s){b("[ion-datetime-button] - An ID associated with an ion-datetime instance is required to function properly.",this.el);return}let e=this.datetimeEl=document.getElementById(s);if(!e){b(`[ion-datetime-button] - No ion-datetime instance found for ID '${s}'.`,this.el);return}if(e.tagName!=="ION-DATETIME"){b(`[ion-datetime-button] - Expected an ion-datetime instance for ID '${s}' but received '${e.tagName.toLowerCase()}' instead.`,e);return}new IntersectionObserver(t=>{let o=t[0];this.datetimeActive=o.isIntersecting},{threshold:.01}).observe(e);let n=this.overlayEl=e.closest("ion-modal, ion-popover");n&&n.classList.add("ion-datetime-button-overlay"),C(e,()=>{let t=this.datetimePresentation=e.presentation||"date-time";switch(this.setDateTimeText(),f(e,"ionValueChange",this.setDateTimeText),t){case"date-time":case"date":case"month-year":case"month":case"year":this.selectedButton="date";break;case"time-date":case"time":this.selectedButton="time";break}})})}render(){let{color:s,dateText:e,timeText:a,selectedButton:n,datetimeActive:t,disabled:o}=this,c=B(this);return d(A,{key:"11d037e6ab061e5116842970760b04850b42f2c7",class:D(s,{[c]:!0,[`${n}-active`]:t,"datetime-button-disabled":o})},e&&d("button",{key:"08ecb62da0fcbf7466a1f2403276712a3ff17fbc",class:"ion-activatable",id:"date-button","aria-expanded":t?"true":"false",onClick:this.handleDateClick,disabled:o,part:"native",ref:p=>this.dateTargetEl=p},d("slot",{key:"1c04853d4d23c0f1a594602bde44511c98355644",name:"date-target"},e),c==="md"&&d("ion-ripple-effect",{key:"5fc566cd4bc885bcf983ce99e3dc65d7f485bf9b"})),a&&d("button",{key:"c9c5c34ac338badf8659da22bea5829d62c51169",class:"ion-activatable",id:"time-button","aria-expanded":t?"true":"false",onClick:this.handleTimeClick,disabled:o,part:"native",ref:p=>this.timeTargetEl=p},d("slot",{key:"147a9d2069dbf737f6fc64787823d6d5af5aa653",name:"time-target"},a),c==="md"&&d("ion-ripple-effect",{key:"70a5e25b75ed90ac6bba003468435f67aa9d8f0a"})))}get el(){return _(this)}};return x.style={ios:F,md:H},x})();export{q as ion_datetime_button};
