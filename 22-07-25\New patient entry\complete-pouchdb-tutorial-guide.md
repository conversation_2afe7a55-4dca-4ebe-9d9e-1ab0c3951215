# Complete PouchDB Tutorial Guide for Beginners

## Introduction

PouchDB is an open-source JavaScript database inspired by Apache CouchDB that is designed to run well within the browser. It enables applications to store data locally while offline, then synchronize it with a remote database when online, providing seamless offline-first experiences.

This tutorial will guide you through the basics of PouchDB, from installation to advanced usage, with easy-to-understand examples.

---

## What is PouchDB?

- A JavaScript database that runs in the browser or Node.js.
- Stores data locally using IndexedDB or WebSQL.
- Syncs data with CouchDB or compatible servers.
- Supports CRUD operations, queries, and real-time sync.
- Ideal for offline-first web and mobile apps.

---

## Why Use PouchDB?

- Offline capability: Work without internet connection.
- Data synchronization: Sync local data with remote CouchDB.
- Simple API: Easy to use with JavaScript.
- Cross-platform: Works in browsers, Node.js, and mobile apps.
- Conflict resolution: Handles data conflicts gracefully.

---

## Installation

### Using npm

```bash
npm install pouchdb
```

### Using CDN

Add this script tag in your HTML:

```html
<script src="https://cdn.jsdelivr.net/npm/pouchdb@7.3.0/dist/pouchdb.min.js"></script>
```

---

## Creating a Database

```javascript
// In Node.js or browser with module bundler
const PouchDB = require('pouchdb');
const db = new PouchDB('my_database');

// Or in browser with CDN
var db = new PouchDB('my_database');
```

---

## Adding Documents

Documents are JSON objects with a unique `_id`.

```javascript
const doc = {
  _id: '001',
  title: 'My first document',
  content: 'Hello, PouchDB!'
};

db.put(doc).then(response => {
  console.log('Document added', response);
}).catch(err => {
  console.error(err);
});
```

---

## Retrieving Documents

```javascript
db.get('001').then(doc => {
  console.log('Document:', doc);
}).catch(err => {
  console.error(err);
});
```

---

## Updating Documents

To update, you need the current `_rev` revision.

```javascript
db.get('001').then(doc => {
  doc.content = 'Updated content';
  return db.put(doc);
}).then(response => {
  console.log('Document updated', response);
}).catch(err => {
  console.error(err);
});
```

---

## Deleting Documents

```javascript
db.get('001').then(doc => {
  return db.remove(doc);
}).then(response => {
  console.log('Document deleted', response);
}).catch(err => {
  console.error(err);
});
```

---

## Querying Documents

Use `allDocs` to get all documents:

```javascript
db.allDocs({ include_docs: true }).then(result => {
  result.rows.forEach(row => {
    console.log(row.doc);
  });
}).catch(err => {
  console.error(err);
});
```

---

## Synchronizing with Remote CouchDB

```javascript
const remoteDB = new PouchDB('http://localhost:5984/my_database');

db.sync(remoteDB, {
  live: true,
  retry: true
}).on('change', info => {
  console.log('Sync change', info);
}).on('error', err => {
  console.error('Sync error', err);
});
```

---

## Handling Conflicts

PouchDB and CouchDB handle conflicts by storing conflicting revisions. You can resolve conflicts by:

- Fetching conflicting revisions.
- Merging changes manually.
- Deleting unwanted revisions.

Example to get conflicts:

```javascript
db.get('001', { conflicts: true }).then(doc => {
  if (doc._conflicts) {
    console.log('Conflicts:', doc._conflicts);
  }
});
```

---

## Best Practices

- Use meaningful `_id` values.
- Handle errors gracefully.
- Use live sync for real-time updates.
- Clean up old revisions if needed.
- Secure remote CouchDB with authentication.

---

## Simple Example

```javascript
const db = new PouchDB('mydb');

async function addDoc() {
  try {
    const doc = { _id: '001', name: 'John Doe' };
    const response = await db.put(doc);
    console.log('Added:', response);
  } catch (err) {
    console.error(err);
  }
}

async function getDoc() {
  try {
    const doc = await db.get('001');
    console.log('Got:', doc);
  } catch (err) {
    console.error(err);
  }
}

addDoc();
getDoc();
```

---

## Project Files and Explanation

### 1. `src/app/services/pouchdb.service.ts`

This service encapsulates all PouchDB operations for patient data.

```typescript
import { Injectable } from '@angular/core';
import { mPatientData } from '../pages/patient-entry/patient-entry';
import { generateUUIDv4 } from '../shared/utils';
import PouchDB from 'pouchdb-browser';

@Injectable({
  providedIn: 'root',
})
export class PouchdbService {
  newpatient: mPatientData | null = null;
  private db: PouchDB.Database;

  constructor() {
    this.db = new PouchDB('register_patient');
  }

  // Fetch all patients
  async getAllPatient(): Promise<mPatientData[]> {
    const result = await this.db.allDocs({ include_docs: true });
    return result.rows.map((r) => r.doc as mPatientData);
  }

  // Add a new patient
  async addPatient(patient: mPatientData): Promise<any> {
    patient._id = generateUUIDv4();
    return await this.db.put(patient);
  }

  // Update existing patient
  async updatePatient(patient: mPatientData): Promise<any> {
    return await this.db.put(patient);
  }

  // Delete patient
  async deletePatient(patient: mPatientData): Promise<any> {
    if (!patient._id || !patient._rev) {
      throw new Error('Patient must have _id and _rev to be deleted');
    }
    return this.db.remove(patient._id, patient._rev);
  }

  // Get single patient
  async getPatient(id: string): Promise<mPatientData> {
    return await this.db.get(id);
  }

  // Sync data with remote server API (example placeholder)
  async syncWithServer(): Promise<void> {
    try {
      const response = await fetch('https://jsonplaceholder.typicode.com/posts');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      console.log('Data synced from server:', data);
      // TODO: Implement logic to merge/sync data with local PouchDB
    } catch (error) {
      console.error('Failed to sync with server:', error);
    }
  }
}
```

### 2. `src/app/pages/patient-entry/patient-entry.page.ts`

This page component uses the PouchdbService to manage patient data and camera capture.

- Imports PouchdbService and other Angular modules.
- Defines reactive form for patient data.
- Implements methods to start/stop camera, capture images, and manage patient records.
- Uses ViewChild to access video and canvas elements for image capture.

(Full code is available in the project file `src/app/pages/patient-entry/patient-entry.page.ts`)

### 3. `src/app/pages/patient-entry/patient-entry.page.html`

This HTML template provides the UI for patient entry, including:

- Form inputs for patient details.
- Sections for capturing patient image and uploading documents.
- Buttons to start camera, take snapshot, and upload files.
- Video and canvas elements for live camera preview and image capture.

(Full code is available in the project file `src/app/pages/patient-entry/patient-entry.page.html`)

---

## How Files Are Imported and Used

- The `PouchdbService` is provided in root and injected into the `PatientEntryPage` component.
- The component calls service methods like `getAllPatient()`, `addPatient()`, `updatePatient()`, and `deletePatient()` to manage data.
- The component handles UI interactions and camera access, updating the form and syncing with the service.
- The HTML template binds to the component's form and methods for user interaction.

---

If you want, I can help create a sample project or further detailed explanations for each file. Please let me know how you want to proceed.
