var o=class{constructor(){this.m=new Map}reset(e){this.m=new Map(Object.entries(e))}get(e,t){let r=this.m.get(e);return r!==void 0?r:t}getBoolean(e,t=!1){let r=this.m.get(e);return r===void 0?t:typeof r=="string"?r==="true":!!r}getNumber(e,t){let r=parseFloat(this.m.get(e));return isNaN(r)?t!==void 0?t:NaN:r}set(e,t){this.m.set(e,t)}},c=new o,g=n=>{try{let e=n.sessionStorage.getItem(a);return e!==null?JSON.parse(e):{}}catch{return{}}},R=(n,e)=>{try{n.sessionStorage.setItem(a,JSON.stringify(e))}catch{return}},m=n=>{let e={};return n.location.search.slice(1).split("&").map(t=>t.split("=")).map(([t,r])=>{try{return[decodeURIComponent(t),decodeURIComponent(r)]}catch{return["",""]}}).filter(([t])=>u(t,i)).map(([t,r])=>[t.slice(i.length),r]).forEach(([t,r])=>{e[t]=r}),e},u=(n,e)=>n.substr(0,e.length)===e,i="ionic:",a="ionic-persist-config",s=function(n){return n.OFF="OFF",n.ERROR="ERROR",n.WARN="WARN",n}(s||{}),f=(n,...e)=>{let t=c.get("logLevel",s.WARN);if([s.WARN].includes(t))return console.warn(`[Ionic Warning]: ${n}`,...e)},p=(n,...e)=>{let t=c.get("logLevel",s.ERROR);if([s.ERROR,s.WARN].includes(t))return console.error(`[Ionic Error]: ${n}`,...e)},d=(n,...e)=>console.error(`<${n.tagName.toLowerCase()}> must be used inside ${e.join(" or ")}.`);export{c as a,g as b,R as c,m as d,f as e,p as f,d as g};
