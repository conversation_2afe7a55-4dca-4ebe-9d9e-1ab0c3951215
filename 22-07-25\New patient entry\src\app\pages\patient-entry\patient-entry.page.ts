import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { IonicModule, ToastController } from '@ionic/angular';
import {
  FormBuilder,
  FormGroup,
  FormArray,
  FormControl,
  ReactiveFormsModule,
  FormsModule,
} from '@angular/forms';
import { IonRadioGroup } from '@ionic/angular/standalone';
import { mPatientData, Document } from './patient-entry';
import { PouchdbService } from 'src/app/services/pouchdb.service';
import { addIcons } from 'ionicons';
import { createOutline, trashOutline } from 'ionicons/icons';
import { lastValueFrom } from 'rxjs';

// ================================
// INTERFACES & TYPES
// ================================

// Interface for uploaded documents (frontend UI)
interface UploadedDoc {
  preview: string;
  type: string;
  draftType?: string;
  date: Date;
  time: string;
  isEditing?: boolean;
}

// Interface for backend document structure (without 'data' field)
interface BackendDocument {
  id: number;
  patientid: number;
  imagepath: string;
  S3URL: string;
  fileName: string;
  fileType: string;
  type: string;
}

// Interface for save patient data request (similar to temperature pattern)
interface SavePatientDataRequest extends mPatientData {
  id: string;
  action: string;
  timestamp: string;
  synced: boolean;
}


@Component({
  selector: 'app-patient-entry',
  templateUrl: './patient-entry.page.html',
  styleUrls: ['./patient-entry.page.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    IonicModule,
    CommonModule,
    HttpClientModule,
    // IonRadioGroup,
  ],
})

export class PatientEntryPage implements OnInit {

  // ================================
  // COMPONENT PROPERTIES
  // ================================

  // Form and Data Properties
  patientForm!: FormGroup;
  submittedPatients: mPatientData[] = [];
  editIndex: number | null = null;

  // UI State Properties
  activeSection: string = 'section0';
  currentStep = 1;

  // Camera Properties
  videoDevices: MediaDeviceInfo[] = [];
  selectedCameraId: string = '';
  mediaStream: any;
  photoPreviewUrl: string | null = null;
  isCameraActive: boolean = false;
  showCameraDropdown: boolean = false;

  // Document Upload Properties
  uploadedDocs: UploadedDoc[] = [];
  docTypeControls: FormControl[] = [];
  capturing = false;

  // Master Data Properties
  countryList: any[] = [];
  stateList: any[] = [];
  districtList: any[] = [];
  blockList: any[] = [];
  villageList: any[] = [];
  masterData: any = {};


  // ================================
  // CONSTRUCTOR & LIFECYCLE
  // ================================

  constructor(
    public objPouchdbService: PouchdbService,
    private fb: FormBuilder,
    private toastController: ToastController,
    private http: HttpClient
  ) {
    addIcons({
      'create-outline': createOutline,
      'trash-outline': trashOutline,
    });
  }

  // ================================
  // STEP NAVIGATION METHODS
  // ================================

  nextStep() {
    if (this.currentStep < 4) this.currentStep++;
    // Auto-start camera when entering step 3 (Patient Image)
    if (this.currentStep === 3) {
      this.openCamera();
    }
  }

  prevStep() {
    if (this.currentStep > 1) this.currentStep--;
    if (this.currentStep === 3) {
      this.openCamera();
    } else {
      this.stopCamera();
      this.isCameraActive = false;
      this.showCameraDropdown = false;
    }
  }

  // ================================
  // DOCUMENT UPLOAD METHODS
  // ================================

  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onFileDrop(event: DragEvent) {
    event.preventDefault();
    if (event.dataTransfer?.files) {
      this.addFiles(event.dataTransfer.files);
    }
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files) this.addFiles(input.files);
  }

  addFiles(files: FileList) {
    Array.from(files).forEach(file => {
      const reader = new FileReader();
      reader.onload = () => {
        const now = new Date();
        const docIndex = this.uploadedDocs.length;

        // Create a new FormControl for this document
        const docTypeControl = new FormControl('');
        this.docTypeControls.push(docTypeControl);

        // Add the document with editing mode enabled
        this.uploadedDocs.push({
          preview: reader.result as string,
          type: '',
          draftType: '',
          date: now,
          time: now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          isEditing: true //  immediately in edit mode
        });

        console.log('Document added in edit mode:', this.uploadedDocs[docIndex]);
      };
      reader.readAsDataURL(file);
    });
  }
// private addFiles(files: FileList) {
//     Array.from(files).forEach(file => {
//       const reader = new FileReader()
//       reader.onload = () => {
//         const now = new Date()
//         this.uploadedDocs.push({
//           preview: reader.result as string,
//           type: '',
//           date: now,
//           time: now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
//         })

//         // Also add to documents FormArray for form submission
//         this.documents.push(
//           this.fb.group({
//             fileName: file.name,
//             fileType: file.type,
//             data: reader.result,
//             type: this.patientForm.value.imageType || 'Document',
//           })
//         );
//       }
//       reader.readAsDataURL(file)
//     })
//   }

  // Get FormControl for document type at specific index
  getDocTypeControl(index: number): FormControl {
    // Ensure we have enough controls
    while (this.docTypeControls.length <= index) {
      this.docTypeControls.push(new FormControl(''));
    }
    return this.docTypeControls[index];
  }

  editDoc(index: number) {
    const doc = this.uploadedDocs[index];
    doc.isEditing = true;
    // Set the current type in the FormControl
    this.getDocTypeControl(index).setValue(doc.type);
    console.log('Editing document at index:', index);
  }

  deleteDoc(index: number) {
    this.uploadedDocs.splice(index, 1);
    // Also remove the corresponding FormControl
    if (index < this.docTypeControls.length) {
      this.docTypeControls.splice(index, 1);
    }
    // Also remove from documents FormArray if needed
    if (index < this.documents.length) {
      this.documents.removeAt(index);
    }
    console.log('Document deleted at index:', index);
  }

  cancelEdit(index: number) {
    const doc = this.uploadedDocs[index];
    doc.isEditing = false;
    // Reset the FormControl to the original type
    this.getDocTypeControl(index).setValue(doc.type);
    console.log('Edit cancelled for document at index:', index);
  }

  saveEdit(index: number) {
    const doc = this.uploadedDocs[index];
    const selectedType = this.getDocTypeControl(index).value;

    if (selectedType && selectedType.trim() !== '') {
      doc.type = selectedType;
      doc.isEditing = false;
      console.log('Document type saved:', doc.type, 'at index:', index);

      // Generate imagepath for backend compatibility
      const patientId = this.patientForm.get('patientid')?.value || 0;
      const documentImagePath = this.generateDocumentImagePath(index, patientId);

      // Add to documents FormArray for form submission if not already added
      const existingDocIndex = this.documents.controls.findIndex((control: any) =>
        control.value.fileName === `Document-${index}-${doc.date.getTime()}.png`
      );

      if (existingDocIndex === -1) {
        this.documents.push(
          this.fb.group({
            id: 0,
            patientid: patientId,
            fileName: `Document-${index}-${doc.date.getTime()}.png`,
            fileType: 'image/png',
            data: doc.preview, // Keep for local storage
            imagepath: documentImagePath, //  Add imagepath for backend
            S3URL: '', // Will be populated by backend
            type: doc.type,
          })
        );
      } else {
        // Update existing document type and imagepath
        this.documents.at(existingDocIndex).patchValue({
          type: doc.type,
          imagepath: documentImagePath
        });
      }
    } else {
      console.warn('Please select a document type before saving');
      // You could show a toast message here
    }
  }


  async startCapture() {
    // This function is used in HTML template for capture image functionality
    console.log('Start capture clicked');

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      // You can implement camera capture for documents here
      // For now, we'll just log that capture was started
      console.log('Camera stream started for document capture');

      // Stop the stream immediately for now (you can implement full capture later)
      stream.getTracks().forEach(track => track.stop());
    } catch (error) {
      console.error('Error starting camera for document capture:', error);
    }
  }

  // ================================
  // CAMERA METHODS
  // ================================

  // Open camera functionality
  async openCamera() {
    this.isCameraActive = true;
    this.showCameraDropdown = false;
    await this.startCamera(this.selectedCameraId);
  }

  // Retake image functionality
  retakeImage() {
    this.photoPreviewUrl = null;
    this.isCameraActive = true;
    this.showCameraDropdown = false;
    this.startCamera(this.selectedCameraId);
  }

  // Toggle camera dropdown
  toggleCameraDropdown() {
    this.showCameraDropdown = !this.showCameraDropdown;
  }

  // Select camera from dropdown
  selectCamera(deviceId: string) {
    this.selectedCameraId = deviceId;
    this.showCameraDropdown = false;
    this.startCamera(deviceId);
  }

  // ================================
  // VIEW CHILD REFERENCES
  // ================================

  @ViewChild('video', { static: false }) video!: ElementRef;
  @ViewChild('canvas', { static: false }) canvas!: ElementRef;
  @ViewChild('uploadVideo', { static: false }) uploadVideo!: ElementRef;


  async ngOnInit() {
    this.patientForm =this.fb.group(new mPatientData());
    // this.patientForm = this.fb.group({
    //   _id: [''],
    //   _rev: [null],
    //   domainwisepid: [0],
    //   patientid: [0],
    //   first_name: [''],
    //   last_name: [''],
    //   date_of_birth: [''],
    //   age: [''],
    //   ageYears: [''],
    //   gender: [''],
    //   maritalstatus: [''],
    //   height: [''],
    //   weight: [''],
    //   mobile: [''],
    //   email: [''],
    //   head_of_household_fname: [''],
    //   head_of_household_lname: [''],
    //   country: [''],
    //   state: [''],
    //   district: [''],
    //   block: [''],
    //   village: [''],
    //   address: [''],
    //   projid: [''],
    //   head_of_household_mobile: [''],
    //   isAbhaPatient: [false],
    //   profile: this.fb.group({
    //     patientid: [0],
    //     imagepath: [''],
    //     S3URL: [''],
    //   }),
    //   pastrecord: [null],
    //   createdat: [''],
    //   createdby: [''],
    //   domain: [0],
    //   uid: [''],
    //   prefix: [null],
    //   EhealthId: [''],
    //   MRN: [''],
    //   password: [''],
    //   consentformcheckstatus: [0],
    //   fingerPrintTemplate: [''],
    //   health_number: [''],
    //   health_address: [''],
    //   unique_id: [null],
    //   nationalId: [null],
    //   ethnicity: [null],
    //   subscriptionDetails: this.fb.group({
    //     subscribedId: [0],
    //     familycardid: [null],
    //     freeSubcriptionAllocated: [0],
    //     completedFreeSubcrition: [0],
    //     remainingSubcription: [0],
    //     isActive: [null],
    //     subcriptionName: [null],
    //     subscriptionPlanActivatedOn: [null],
    //     subscriptionExpiredOn: [null],
    //     isExpaired: [0],
    //   }),
    //   localId: [''],
    //   patient_status: [null],
    //   patient_title: [null],
    //   postCode: [null],
    //   centerName: [null],
    //   status: [null],
    //   isSync: [false],
    //   documents: this.fb.array([]), // FormArray for uploaded documents (not patient image)
    //   type: ['patient'],
      
    // });
    

    // Load all patients on init
    await this.loadPatients();

    // Load master data on init
    await this.loadMasterData();

    // Enumerate video input devices (cameras)
    if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        this.videoDevices = devices.filter(
          (device) => device.kind === 'videoinput'
        );
        if (this.videoDevices.length > 0) {
          this.selectedCameraId = this.videoDevices[0].deviceId;
          // Removed automatic startCamera call to delay camera access request until user clicks start
          // this.startCamera(this.selectedCameraId);
        }
      } catch (error) {
        console.error('Error enumerating devices:', error);
      }
    } else {
      console.warn('Media Devices API not supported.');
    }
  }

  /**  Load Master Data JSON into PouchDB (if not already saved) */
  private async loadMasterData() {
    try {
      this.objPouchdbService.getMasterData().subscribe({
        next: (data) => {
          console.log(' Master Data already exists in PouchDB');
          this.processMasterData(data);
        },
        error: () => {
          console.log('⚠ Master Data not found → Loading from assets...');
          this.http
            .get('assets/RemediNovaAPI.json')
            .subscribe((jsonData: any) => {
              this.objPouchdbService
                .addOrUpdateMasterData(jsonData)
                .subscribe(() => {
                  console.log(' Master Data saved in PouchDB');
                  this.processMasterData(jsonData);
                });
            });
        },
      });
    } catch (err) {
      console.error(' Error loading master data:', err);
    }
  }

  /**  Process and extract master data from the JSON structure */
  private processMasterData(data: any) {
    try {
      console.log(' Processing master data:', data);

      // Extract tables from the nested data structure
      if (data.data && Array.isArray(data.data)) {
        console.log(' Found data array with', data.data.length, 'items');

        // Find each table in the data array
        data.data.forEach((item: any, index: number) => {
          if (item.tblcountry) {
            this.masterData.tblcountry = item.tblcountry;
            console.log(
              ' Found tblcountry at index',
              index,
              'with',
              item.tblcountry.length,
              'countries'
            );
          }
          if (item.tblstate) {
            this.masterData.tblstate = item.tblstate;
            console.log(
              ' Found tblstate at index',
              index,
              'with',
              item.tblstate.length,
              'states'
            );
          }
          if (item.tbldistrict) {
            this.masterData.tbldistrict = item.tbldistrict;
            console.log(
              ' Found tbldistrict at index',
              index,
              'with',
              item.tbldistrict.length,
              'districts'
            );
          }
          if (item.tblblock) {
            this.masterData.tblblock = item.tblblock;
            console.log(
              ' Found tblblock at index',
              index,
              'with',
              item.tblblock.length,
              'blocks'
            );
          }
          if (item.tblvillage) {
            this.masterData.tblvillage = item.tblvillage;
            console.log(
              ' Found tblvillage at index',
              index,
              'with',
              item.tblvillage.length,
              'villages'
            );
          }
        });
      } else {
        // If data is already processed and stored directly
        console.log(' Using direct data structure');
        this.masterData = data;
      }

      // Set country list for dropdown (filter out deleted countries)
      if (this.masterData.tblcountry) {
        this.countryList = this.masterData.tblcountry.filter(
          (country: any) => country.IsDeleted === '0'
        );
        console.log(' Countries loaded:', this.countryList.length);
        console.log(' Sample countries:', this.countryList.slice(0, 3));

        // Temporary alert to verify data loading
        if (this.countryList.length > 0) {
          console.log(' SUCCESS: Countries dropdown should now work!');
        }
      } else {
        console.log(' No tblcountry found in master data');
        console.log(
          ' Available keys in masterData:',
          Object.keys(this.masterData)
        );
      }

      console.log(' Master Data processed successfully');
      console.log(' Master data structure:', Object.keys(this.masterData));
    } catch (err) {
      console.error(' Error processing master data:', err);
    }
  }


  async loadPatients() {
    try {
      this.objPouchdbService
        .getRecordsByType<mPatientData>('patient')
        .subscribe({
          next: (patients) => {
            this.submittedPatients = patients;
            // Auto-sync unsynced patients with backend
            this.syncUnsyncedPatients();
          },
          error: (err) => console.error('Failed to load patients:', err),
        });
    } catch (error) {
      console.error('Failed to load patients:', error);
    }
  }

  /**
   * Sync all unsynced patients with backend API (using new sync pattern)
   */
  private async syncUnsyncedPatients() {
    try {
      console.log('🔄 Starting sync of unsynced patients...');
      this.syncPatientDataToServer();
    } catch (error) {
      console.error('❌ Error syncing unsynced patients:', error);
    }
  }

  // ================================
  // UTILITY METHODS
  // ================================

  // FormArray getter for uploaded images
  get documents(): FormArray {
    return this.patientForm.get('documents') as FormArray;
  }

  toggleSection(section: string) {
    this.activeSection = this.activeSection === section ? '' : section;
  }

  onCountryChange(event: any) {
    const selectedCountryId = event.target.value;
    this.patientForm.patchValue({ state: '', district: '', block: '', village: '' });

    //  Use already loaded masterData
    if (this.masterData.tblstate) {
      this.stateList = this.masterData.tblstate.filter(
        (s: any) => s.CountryId === selectedCountryId && s.IsDeleted === '0'
      );
      console.log(
        ' States loaded for country:',
        selectedCountryId,
        this.stateList.length
      );
    } else {
      this.stateList = [];
      console.log(' No states data available');
    }

    this.districtList = [];
    this.blockList = [];
    this.villageList = [];
  }

  onStateChange(event: any) {
    const selectedStateId = event.target.value;
    this.patientForm.patchValue({ district: '', block: '', village: '' });

    //  Use already loaded masterData
    if (this.masterData.tbldistrict) {
      this.districtList = this.masterData.tbldistrict.filter(
        (d: any) => d.StateId === selectedStateId && d.IsDeleted === '0'
      );
      console.log(
        ' Districts loaded for state:',
        selectedStateId,
        this.districtList.length
      );
    } else {
      this.districtList = [];
      console.log(' No districts data available');
    }

    this.blockList = [];
    this.villageList = [];
  }

  onDistrictChange(event: any) {
    const selectedDistrictId = event.target.value;
    this.patientForm.patchValue({ block: '', village: '' });

    //  Use already loaded masterData
    if (this.masterData.tblblock) {
      this.blockList = this.masterData.tblblock.filter(
        (b: any) => b.DistrictId === selectedDistrictId && b.IsDeleted === '0'
      );
      console.log(
        ' Blocks loaded for district:',
        selectedDistrictId,
        this.blockList.length
      );
    } else {
      this.blockList = [];
      console.log(' No blocks data available');
    }

    this.villageList = [];
  }

  onBlockChange(event: any) {
    const selectedBlockId = event.target.value;
    this.patientForm.patchValue({ village: '' });

    //  Use already loaded masterData
    if (this.masterData.tblvillage) {
      this.villageList = this.masterData.tblvillage.filter(
        (v: any) => v.BlockId === selectedBlockId && v.IsDeleted === '0'
      );
      console.log(
        ' Villages loaded for block:',
        selectedBlockId,
        this.villageList.length
      );
    } else {
      this.villageList = [];
      console.log(' No villages data available');
    }
  }

  async startCamera(deviceId?: string) {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      alert('Camera API not supported');
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: deviceId ? { deviceId: { exact: deviceId } } : true,
      });

      this.mediaStream = stream; // Assign stream to mediaStream property

      this.video.nativeElement.srcObject = stream;
      await this.video.nativeElement.play();
    } catch (err) {
      console.error('Error starting camera:', err);
    }
  }
  switchCamera(event: any) {
    this.selectedCameraId = event.target.value;
    if (this.selectedCameraId) {
      this.startCamera(this.selectedCameraId);
    }
  }

  

  captureImage() {
    const videoEl = this.video.nativeElement;
    const canvasEl = this.canvas.nativeElement;

    // Dynamically set canvas dimensions to match the video stream
    canvasEl.width = videoEl.videoWidth;
    canvasEl.height = videoEl.videoHeight;

    const context = canvasEl.getContext('2d');
    if (!context) {
      console.error('Failed to get canvas context.');
      return;
    }

    // Draw the current video frame onto the canvas
    context.drawImage(videoEl, 0, 0, canvasEl.width, canvasEl.height);

    // Convert the canvas to a base64 PNG image
    const imageData = canvasEl.toDataURL('image/png');

    // Generate patient image path for backend compatibility
    const timestamp = Date.now();
    const patientImagePath = `patientdata/images/profilepic/${timestamp}_pat_${this.patientForm.get('patientid')?.value || 'new'}`;

    // Update profile with patient image information (backend format)
    this.patientForm.get('profile')?.patchValue({
      imagepath: patientImagePath,
      S3URL: '', // Will be populated by backend after upload
      patientid: this.patientForm.get('patientid')?.value || 0
    });

    // Store image data for preview (frontend only)
    this.photoPreviewUrl = imageData;

    // Stop the camera after capturing and update state
    this.stopCamera();
    this.isCameraActive = false;
    this.showCameraDropdown = false;

    // Clear the video element's srcObject to release the camera
    if (this.video && this.video.nativeElement) {
      this.video.nativeElement.srcObject = null;
    }

    console.log('Patient image captured and stored in profile structure for backend compatibility');
    console.log('Profile data:', this.patientForm.get('profile')?.value);
  }

  stopCamera() {
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach((track: any) => track.stop());
      this.mediaStream = null;
    }
    this.isCameraActive = false;
    this.showCameraDropdown = false;
  }



  removeDocument(index: number) {
    this.documents.removeAt(index);
  }

  // ================================
  // BACKEND INTEGRATION METHODS
  // ================================

  /**
   * Calculate age from date of birth
   */
  private calculateAge(dateOfBirth: string): { age: string; ageYears: string } {
    if (!dateOfBirth) {
      return { age: '', ageYears: '' };
    }

    const birthDate = new Date(dateOfBirth);
    const today = new Date();

    let years = today.getFullYear() - birthDate.getFullYear();
    let months = today.getMonth() - birthDate.getMonth();
    let days = today.getDate() - birthDate.getDate();

    // Adjust for negative days
    if (days < 0) {
      months--;
      const lastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
      days += lastMonth.getDate();
    }

    // Adjust for negative months
    if (months < 0) {
      years--;
      months += 12;
    }

    const age = `${years} Years, ${months} Months, ${days} Days`;
    const ageYears = `${years} years`;

    return { age, ageYears };
  }

  /**
   * Convert string values to proper types and handle null values
   */
  private sanitizeValue(value: any): any {
    if (value === 'null' || value === 'NULL') {
      return null;
    }
    if (value === '' || value === undefined) {
      return value === '' ? '' : null;
    }
    return value;
  }

  /**
   * Generate document imagepath for backend
   */
  private generateDocumentImagePath(docIndex: number, patientId: number): string {
    const timestamp = Date.now();
    return `patientdata/documents/doc_${patientId}_${timestamp}_${docIndex}`;
  }



  // ================================
  // PATIENT CRUD OPERATIONS
  // ================================

  async savePatients() {
    console.log('Patient save completed!');

    // Prepare patient data following temperature pattern
    const formValue = this.patientForm.value;
    const saveData: SavePatientDataRequest = {
      ...formValue,
      id: 'patient' + new Date().getTime(), // unique id
      type: 'patient',
      action: 'savePatient',
      timestamp: new Date().toISOString(),
      synced: false
    };

    // Remove frontend-specific fields
    delete (saveData as any).patientImage;
    delete (saveData as any).imageType;
    delete (saveData as any)._doc_id_rev;

    // Set initial sync status
    saveData.isSync = false;

    // Ensure documents array has proper structure
    if (this.documents.value && this.documents.value.length > 0) {
      saveData.documents = this.documents.value.map((doc: any, index: number) => ({
        id: doc.id || 0,
        patientid: doc.patientid || saveData.patientid || 0,
        fileName: doc.fileName,
        fileType: doc.fileType,
        data: doc.data, // Keep for local storage
        type: doc.type,
        imagepath: doc.imagepath || this.generateDocumentImagePath(index, saveData.patientid || 0),
        S3URL: doc.S3URL || ''
      }));
    }

    // Calculate age from date_of_birth
    if (saveData.date_of_birth) {
      const ageData = this.calculateAge(saveData.date_of_birth);
      saveData.age = ageData.age;
      saveData.ageYears = ageData.ageYears;
    }

    console.log('Saving Patient Data:', saveData); // will show in browser console

      if (this.editIndex === null) {
        // New patient
        if (!saveData._rev) delete saveData._rev;
        if (!saveData._id || saveData._id.trim() === '') delete saveData._id;

        this.objPouchdbService.addRecord(saveData).subscribe({
          next: (res) => {
            console.log('Saved patient data to PouchDB:', res);
            this.syncPatientDataToServer();
            this.objPouchdbService.getAllRecords<SavePatientDataRequest>().subscribe(records => {
              console.log('All saved patient records:', records);
            });
          },
          error: (err) => {
            console.error('Failed to save patient data:', err);
          }
        });
      } else {
        // Update existing patient
        if (!saveData._id) throw new Error('Invalid _id for update');

        const latest = await lastValueFrom(
          this.objPouchdbService.getRecordById<mPatientData>(saveData._id)
        );
        saveData._rev = latest._rev;

        this.objPouchdbService.updateRecord(saveData).subscribe({
          next: (res) => {
            console.log('Updated patient data in PouchDB:', res);
            this.syncPatientDataToServer();
            this.editIndex = null;
          },
          error: (err) => {
            console.error('Failed to update patient data:', err);
          }
        });
      }

      await this.loadPatients();
      this.patientForm.reset();
      this.photoPreviewUrl = null;

    } catch (error) {
      console.error('❌ Failed to save patient:', error);
      const toast = await this.toastController.create({
        message: 'Failed to save patient.',
        duration: 3000,
        color: 'danger',
      });
      await toast.present();
    }
  }

  /**
   * Sync unsynced patients with backend API (similar to temperature sync pattern)
   */
  syncPatientDataToServer() {
    this.objPouchdbService.getAllRecords<SavePatientDataRequest>().subscribe(records => {
      const unsyncedRecords = records.filter(record => record.type === 'patient' && !record.synced);

      console.log(`🔄 Found ${unsyncedRecords.length} unsynced patient records`);

      unsyncedRecords.forEach(record => {
        // Prepare data for backend (remove frontend-specific fields)
        const backendPayload: any = { ...record };

        // Remove PouchDB specific fields
        delete backendPayload._id;
        delete backendPayload._rev;
        delete backendPayload.type;
        delete backendPayload.isSync;
        delete backendPayload.id;
        delete backendPayload.action;
        delete backendPayload.timestamp;
        delete backendPayload.synced;

        // Apply backend transformations
        if (backendPayload.gender) {
          backendPayload.gender = backendPayload.gender.toLowerCase();
        }
        if (backendPayload.maritalstatus) {
          backendPayload.maritalstatus = backendPayload.maritalstatus.toLowerCase();
        }

        // Convert location fields to numbers
        backendPayload.country = parseInt(backendPayload.country) || 0;
        backendPayload.state = parseInt(backendPayload.state) || 0;
        backendPayload.district = parseInt(backendPayload.district) || 0;
        backendPayload.block = parseInt(backendPayload.block) || 0;
        backendPayload.village = parseInt(backendPayload.village) || 0;

        // Replace "null" strings with real null
        Object.keys(backendPayload).forEach(key => {
          if (typeof backendPayload[key] === 'string') {
            backendPayload[key] = this.sanitizeValue(backendPayload[key]);
          }
        });

        // Remove base64 data from documents for backend
        if (backendPayload.documents && Array.isArray(backendPayload.documents)) {
          backendPayload.documents = backendPayload.documents.map((doc: any) => ({
            id: doc.id || 0,
            patientid: doc.patientid || backendPayload.patientid || 0,
            imagepath: doc.imagepath || '',
            S3URL: doc.S3URL || '',
            fileName: doc.fileName || '',
            fileType: doc.fileType || '',
            type: doc.type || ''
            // Remove 'data' field for backend
          }));
        }

        console.log('📤 Syncing patient record:', record._id);

        // Call backend API using HTTP POST
        this.http.post('https://backend-api.com/api/patients', backendPayload)
          .subscribe({
            next: (res: any) => {
              if (res.status === 'success') {
                console.log('✅ Patient sync successful for:', record._id);

                // Mark record as synced
                record.synced = true;

                // Update with backend response data if available
                if (res.data) {
                  const backendData = res.data;

                  // Update profile S3URL if provided by backend
                  if (backendData.profile?.S3URL) {
                    record.profile.S3URL = backendData.profile.S3URL;
                  }

                  // Update documents S3URLs if provided by backend
                  if (backendData.documents && Array.isArray(backendData.documents)) {
                    backendData.documents.forEach((backendDoc: any, index: number) => {
                      if (record.documents[index] && backendDoc.S3URL) {
                        record.documents[index].S3URL = backendDoc.S3URL;
                      }
                    });
                  }
                }

                // Update record in PouchDB with sync flag
                this.objPouchdbService.updateRecord(record).subscribe(() => {
                  console.log('✅ Marked patient record as synced:', record._id);
                });
              }
            },
            error: (error) => {
              console.error('❌ Patient sync failed for:', record._id, error);
            }
          });
      });
    });
  }



  // Load patient data into form for editing
  editPatients(patient: mPatientData, index: number) {
    this.editIndex = index;
    this.patientForm.patchValue({
      ...patient,
      _id: patient._id || '',
      _rev: patient._rev || '',
    });
    // Use profile.S3URL for photo preview if available, fallback to imagepath
    this.photoPreviewUrl = patient.profile?.S3URL || patient.profile?.imagepath || null;
  }

  // Delete patient and refresh list
  async delete(patient: mPatientData) {
    try {
      await lastValueFrom(this.objPouchdbService.deleteRecord(patient));
      await this.loadPatients();

      const toast = await this.toastController.create({
        message: `Patient deleted successfully. Total entries: ${this.submittedPatients.length}`,
        duration: 3000,
        color: 'success',
      });
      await toast.present();
    } catch (error) {
      console.error('Failed to delete patient:', error);
      const toast = await this.toastController.create({
        message: 'Failed to delete patient.',
        duration: 3000,
        color: 'danger',
      });
      await toast.present();
    }
  }

}
